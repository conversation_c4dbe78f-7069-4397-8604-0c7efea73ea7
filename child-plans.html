<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Child Investment Plans for Future | Growwell</title>
    <meta
      name="description"
      content="Secure your child’s future with Growwell tailored child investment plans. Start early to build a strong financial foundation for education and life goals."
    />
    <meta
      name="keywords"
      content="growwell ,child investment plans, education planning, investment planning"
    />
    <meta name="robots" content="child-plans, follow" />
    <link rel="canonical" href="https://growwellimf.com/" />
    <meta
      property="og:title"
      content="Child Investment Plans for Future | Growwell"
    />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://growwellimf.com/" />
    <meta
      property="og:image"
      content="https://growwellimf.com/images/growwell_logo_01.webp"
    />
    <meta name="author" content="Growwell" />
    <meta name="publisher" content="Growwell" />

    <!--google adsense-->
    <meta name="google-adsense-account" content="ca-pub-****************" />
    <script
      async
      src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************"
      crossorigin="anonymous"
    ></script>

    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="css/child-plans.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <!-- Header Section -->
    <header>
      <div class="container">
        <div class="navbar">
          <div class="logo">
            <a href="index.html">
              <img
                src="./images/growwell_logo_01.webp"
                alt="company_logo"
                class="brand-logo"
            /></a>
            <a href="index.html"
              ><h2><span class="highlight">Grow</span>well</h2></a
            >
          </div>

          <div class="hamburger" id="hamburger">
            <div class="bar"></div>
            <div class="bar"></div>
            <div class="bar"></div>
          </div>

          <nav id="nav-menu">
            <ul id="menu-items">
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Products</a>
                <div class="dropdown-menu">
                  <a href="investment-plans.html">Investment Plans</a>
                  <a href="retirement-plans.html">Retirement Plans</a>
                  <a href="child-plans.html">Child Plans</a>
                  <a href="term-insurance.html">Term Insurance</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Renew</a>
                <div class="dropdown-menu">
                  <a href="renew-policy.html">Renew Policy</a>
                  <a href="check-status.html">Check Status</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Claim</a>
                <div class="dropdown-menu">
                  <a href="file-claim.html">File a Claim</a>
                  <a href="track-claim.html">Track Claim</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Help & Support</a>
                <div class="dropdown-menu">
                  <a href="faqs.html">FAQs</a>
                  <a href="contact.html">Contact Us</a>
                  <a href="support.html">Customer Support</a>
                  <a href="advisor.html">Find an Advisor</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">About Us</a>
                <div class="dropdown-menu">
                  <a href="our-story.html">Our Story</a>
                  <a href="leadership.html">Leadership</a>
                  <a href="careers.html">Careers</a>
                </div>
              </li>
            </ul>
          </nav>

          <div class="navbar-actions desktop-only">
            <a href="investment-plans.html" class="btn-primary">Get Started</a>
          </div>
        </div>
      </div>
    </header>
    <!-- Hero Banner for Child Plans -->
    <section class="child-hero">
      <div class="container">
        <div class="child-hero-wrapper centered">
          <span class="hero-badge">Future Planning</span>
          <h1>
            Secure Your Child's Bright
            <span class="gradient-text"> Future</span>
          </h1>
          <p>
            Invest in your child's dreams with our comprehensive child plans
            that provide financial security for their education, marriage, and
            other important life milestones.
          </p>
          <div class="child-hero-cta">
            <a href="advisor.html" class="btn-primary btn-hero"
              >Talk to an Advisor</a
            >
            <a href="#plans" class="btn-secondary btn-hero">Explore Plans</a>
          </div>
        </div>
      </div>
    </section>

    <!-- Child Milestones Section -->
    <section class="child-benefits" id="benefits">
      <div class="container">
        <div class="section-header centered">
          <span class="section-subtitle">Life Milestones</span>
          <h2>
            Planning for Your Child's <span class="journey-txt"> Journey</span>
          </h2>
          <div class="title-underline"></div>
          <p>
            Our child plans are designed to help you finance important
            milestones in your child's life
          </p>
        </div>

        <div class="milestones-grid">
          <div class="milestone-card">
            <div class="milestone-icon">
              <i class="fas fa-graduation-cap"></i>
            </div>
            <h3 class="milestone-title">School Education</h3>
            <p class="milestone-description">
              Ensure your child receives quality primary and secondary education
              without financial constraints.
            </p>
          </div>

          <div class="milestone-card">
            <div class="milestone-icon">
              <i class="fas fa-university"></i>
            </div>
            <h3 class="milestone-title">Higher Education</h3>
            <p class="milestone-description">
              Fund undergraduate, graduate or specialized education in India or
              abroad with adequate financial planning.
            </p>
          </div>

          <div class="milestone-card">
            <div class="milestone-icon">
              <i class="fas fa-briefcase"></i>
            </div>
            <h3 class="milestone-title">Career Startup</h3>
            <p class="milestone-description">
              Provide financial backing for entrepreneurial ventures or career
              establishment costs.
            </p>
          </div>

          <div class="milestone-card">
            <div class="milestone-icon">
              <i class="fas fa-ring"></i>
            </div>
            <h3 class="milestone-title">Marriage</h3>
            <p class="milestone-description">
              Plan ahead for wedding expenses and starting a new chapter in your
              child's life.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Child Plans Section -->
    <section class="plans-section" id="plans">
      <div class="container">
        <div class="section-header centered">
          <span class="section-tag">Our Plans</span>
          <h2><span class="journey-txt">Child</span> Development Plans</h2>
          <p>Choose the perfect plan to support your child's growth journey</p>
        </div>

        <div class="plan-tabs">
          <button class="plan-tab active" data-filter="all">All Plans</button>
          <button class="plan-tab" data-filter="physical">Physical</button>
          <button class="plan-tab" data-filter="cognitive">Cognitive</button>
          <button class="plan-tab" data-filter="emotional">Emotional</button>
        </div>

        <div class="plans-grid">
          <!-- Plan Card 1 -->
          <div class="plan-card" data-category="physical">
            <span class="plan-badge">Physical</span>
            <h3>Growth Essentials</h3>
            <div class="price">
              <span class="currency">₹</span>
              <span class="amount">29</span>
              <span class="period">/month</span>
            </div>
            <ul class="plan-features">
              <li><i class="fas fa-check"></i> Personalized nutrition plans</li>
              <li>
                <i class="fas fa-check"></i> Age-appropriate exercise routines
              </li>
              <li><i class="fas fa-check"></i> Growth tracking tools</li>
              <li><i class="fas fa-check"></i> Monthly consultation</li>
              <li><i class="fas fa-check"></i> Basic health resources</li>
            </ul>
            <div class="plan-actions">
              <a href="#" class="btn btn-plan">Get Started</a>
              <a href="#" class="plan-details" onclick="openPlanModal('growth-essentials')">View Details</a>
            </div>
          </div>

          <!-- Plan Card 2 -->
          <div class="plan-card popular" data-category="cognitive">
            <span class="plan-badge">Cognitive</span>
            <div class="popular-badge">Most Popular</div>
            <h3>Brain Builders</h3>
            <div class="price">
              <span class="currency">₹</span>
              <span class="amount">49</span>
              <span class="period">/month</span>
            </div>
            <ul class="plan-features">
              <li>
                <i class="fas fa-check"></i> Customized learning activities
              </li>
              <li><i class="fas fa-check"></i> Critical thinking exercises</li>
              <li><i class="fas fa-check"></i> Weekly progress reports</li>
              <li>
                <i class="fas fa-check"></i> Educational resources library
              </li>
              <li>
                <i class="fas fa-check"></i> Bi-weekly expert consultation
              </li>
            </ul>
            <div class="plan-actions">
              <a href="#" class="btn btn-plan">Get Started</a>
              <a href="#" class="plan-details" onclick="openPlanModal('brain-builders')">View Details</a>
            </div>
          </div>

          <!-- Plan Card 3 -->
          <div class="plan-card" data-category="emotional">
            <span class="plan-badge">Emotional</span>
            <h3>Emotional Intelligence</h3>
            <div class="price">
              <span class="currency">₹</span>
              <span class="amount">39</span>
              <span class="period">/month</span>
            </div>
            <ul class="plan-features">
              <li><i class="fas fa-check"></i> Emotion recognition tools</li>
              <li><i class="fas fa-check"></i> Social skills development</li>
              <li><i class="fas fa-check"></i> Stress management techniques</li>
              <li><i class="fas fa-check"></i> Monthly parent guidance</li>
              <li>
                <i class="fas fa-check"></i> Child-friendly mindfulness
                activities
              </li>
            </ul>
            <div class="plan-actions">
              <a href="#" class="btn btn-plan">Get Started</a>
              <a href="#" class="plan-details" onclick="openPlanModal('emotional-intelligence')">View Details</a>
            </div>
          </div>

          <!-- Plan Card 4 -->
          <div class="plan-card" data-category="physical cognitive">
            <span class="plan-badge">Physical Cognitive</span>
            <h3>Complete Development</h3>
            <div class="price">
              <span class="currency">₹</span>
              <span class="amount">79</span>
              <span class="period">/month</span>
            </div>
            <ul class="plan-features">
              <li>
                <i class="fas fa-check"></i> All features from Growth Essentials
              </li>
              <li>
                <i class="fas fa-check"></i> All features from Brain Builders
              </li>
              <li><i class="fas fa-check"></i> Weekly expert consultation</li>
              <li><i class="fas fa-check"></i> Priority support</li>
              <li><i class="fas fa-check"></i> Advanced progress analytics</li>
            </ul>
            <div class="plan-actions">
              <a href="#" class="btn btn-plan">Get Started</a>
              <a href="#" class="plan-details" onclick="openPlanModal('complete-development')">View Details</a>
            </div>
          </div>

          <!-- Plan Card 5 -->
          <div class="plan-card" data-category="physical emotional">
            <span class="plan-badge">Physical Emotional</span>
            <h3>Balanced Growth</h3>
            <div class="price">
              <span class="currency">₹</span>
              <span class="amount">69</span>
              <span class="period">/month</span>
            </div>
            <ul class="plan-features">
              <li>
                <i class="fas fa-check"></i> All features from Growth Essentials
              </li>
              <li>
                <i class="fas fa-check"></i> All features from Emotional
                Intelligence
              </li>
              <li><i class="fas fa-check"></i> Weekly expert consultation</li>
              <li><i class="fas fa-check"></i> Family activities guide</li>
              <li>
                <i class="fas fa-check"></i> Quarterly in-depth assessment
              </li>
            </ul>
            <div class="plan-actions">
              <a href="#" class="btn btn-plan">Get Started</a>
              <a href="#" class="plan-details" onclick="openPlanModal('balanced-growth')">View Details</a>
            </div>
          </div>

          <!-- Plan Card 6 -->
          <div
            class="plan-card premium"
            data-category="physical cognitive emotional"
          >
            <span class="plan-badge">Physical Cognitive Emotional</span>
            <div class="premium-badge">Premium</div>
            <h3>Comprehensive Care</h3>
            <div class="price">
              <span class="currency">₹</span>
              <span class="amount">99</span>
              <span class="period">/month</span>
            </div>
            <ul class="plan-features">
              <li>
                <i class="fas fa-check"></i> All features from all other plans
              </li>
              <li>
                <i class="fas fa-check"></i> Personalized development roadmap
              </li>
              <li>
                <i class="fas fa-check"></i> Unlimited expert consultations
              </li>
              <li><i class="fas fa-check"></i> VIP support 24/7</li>
              <li>
                <i class="fas fa-check"></i> Exclusive workshops and webinars
              </li>
            </ul>
            <div class="plan-actions">
              <a href="#" class="btn btn-plan">Get Started</a>
              <a href="#" class="plan-details" onclick="openPlanModal('comprehensive-care')">View Details</a>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Specialized Features -->
    <section class="specialized-features">
      <div class="container">
        <div class="section-header centered">
          <span class="section-subtitle">Key Benefits</span>
          <h2>
            <span class="journey-txt">Special</span> Features of Our Child
            <span class="feature-plans-text">Plans</span>
          </h2>
          <div class="title-underline"></div>
          <p>
            Discover the unique advantages that make our child plans stand out
          </p>
        </div>

        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">
              <i class="fas fa-umbrella"></i>
            </div>
            <h3 class="feature-title">Premium Waiver Benefit</h3>
            <p class="feature-description">
              In case of the policyholder's untimely demise or disability, all
              future premiums are waived while benefits continue as planned.
            </p>
            <a href="#" class="feature-link"
              >Learn more <i class="fas fa-arrow-right"></i
            ></a>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <i class="fas fa-money-bill-wave"></i>
            </div>
            <h3 class="feature-title">Flexible Payouts</h3>
            <p class="feature-description">
              Receive lump sum or staggered payouts aligned with your child's
              educational and other milestone needs.
            </p>
            <a href="#" class="feature-link"
              >Learn more <i class="fas fa-arrow-right"></i
            ></a>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <i class="fas fa-chart-line"></i>
            </div>
            <h3 class="feature-title">Fund Boosters</h3>
            <p class="feature-description">
              Enjoy loyalty additions and wealth boosters that enhance your
              corpus the longer you stay invested with us.
            </p>
            <a href="#" class="feature-link"
              >Learn more <i class="fas fa-arrow-right"></i
            ></a>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <i class="fas fa-hand-holding-usd"></i>
            </div>
            <h3 class="feature-title">Tax Benefits</h3>
            <p class="feature-description">
              Get tax advantages under Section 80C for premiums paid and
              tax-free maturity benefits under Section 10(10D).
            </p>
            <a href="#" class="feature-link"
              >Learn more <i class="fas fa-arrow-right"></i
            ></a>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <i class="fas fa-user-shield"></i>
            </div>
            <h3 class="feature-title">Guardian Benefit</h3>
            <p class="feature-description">
              Option to assign a trusted guardian to manage the funds in case of
              unforeseen circumstances.
            </p>
            <a href="#" class="feature-link"
              >Learn more <i class="fas fa-arrow-right"></i
            ></a>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <i class="fas fa-sync-alt"></i>
            </div>
            <h3 class="feature-title">Flexible Premium Options</h3>
            <p class="feature-description">
              Choose from various premium payment terms and options to suit your
              financial capabilities.
            </p>
            <a href="#" class="feature-link"
              >Learn more <i class="fas fa-arrow-right"></i
            ></a>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq-section">
      <div class="container">
        <div class="section-header centered">
          <span class="section-subtitle">Common Questions</span>
          <h2>Frequently Asked <span class="journey-txt">Questions</span></h2>
          <div class="title-underline"></div>
        </div>

        <div class="faq-content">
          <div class="faq-item">
            <div class="faq-question">
              <span>What is the ideal age to start a child plan?</span>
              <i class="fas fa-chevron-down"></i>
            </div>
            <div class="faq-answer">
              <p>
                The ideal time to start a child plan is as early as possible,
                preferably right after your child's birth. This gives your
                investment the maximum time to grow through the power of
                compounding. However, you can start a child plan anytime before
                your child reaches their late teens to help fund their higher
                education and other milestone expenses.
              </p>
            </div>
          </div>

          <div class="faq-item">
            <div class="faq-question">
              <span
                >How is a child plan different from a regular investment
                plan?</span
              >
              <i class="fas fa-chevron-down"></i>
            </div>
            <div class="faq-answer">
              <p>
                Child plans are specifically designed with features that protect
                your child's future even in your absence. Unlike regular
                investment plans, child plans typically come with premium waiver
                benefits that ensure the plan continues even if something
                happens to the parent/policyholder. Additionally, child plans
                often have structured payout options aligned with educational
                milestones and other specific needs of children.
              </p>
            </div>
          </div>

          <div class="faq-item">
            <div class="faq-question">
              <span
                >Can I withdraw money from my child plan before maturity?</span
              >
              <i class="fas fa-chevron-down"></i>
            </div>
            <div class="faq-answer">
              <p>
                Most of our child plans offer partial withdrawal facilities
                after a lock-in period (typically 5 years). These withdrawals
                can be made for specific child-related expenses like school
                admission fees or other educational needs. However, we recommend
                limiting withdrawals to preserve the long-term growth potential
                of your investment.
              </p>
            </div>
          </div>

          <div class="faq-item">
            <div class="faq-question">
              <span>Are there tax benefits associated with child plans?</span>
              <i class="fas fa-chevron-down"></i>
            </div>
            <div class="faq-answer">
              <p>
                Yes, child plans offer significant tax advantages. The premiums
                paid qualify for tax deduction under Section 80C of the Income
                Tax Act (up to ₹1.5 lakhs annually). Additionally, the maturity
                proceeds or withdrawals from these plans are generally
                tax-exempt under Section 10(10D), subject to certain conditions.
              </p>
            </div>
          </div>

          <div class="faq-item">
            <div class="faq-question">
              <span
                >What happens to the child plan if I miss premium
                payments?</span
              >
              <i class="fas fa-chevron-down"></i>
            </div>
            <div class="faq-answer">
              <p>
                Our child plans come with a grace period (typically 30 days)
                during which you can pay the premium without any penalty. If you
                miss payments beyond the grace period, the policy may lapse or
                become paid-up with reduced benefits. However, you can reinstate
                most lapsed policies within a specified period by paying the
                outstanding premiums with applicable interest.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="cta">
      <div class="container">
        <div class="cta-content">
          <span class="section-subtitle">Start Investing Today</span>
          <h2>Ready to Grow Your Wealth?</h2>
          <p>
            Schedule a free consultation with our investment advisors to get
            started.
          </p>
          <div class="cta-buttons">
            <a href="#contact" class="btn btn-primary"
              ><i class="fas fa-calendar-alt"></i> Schedule Consultation</a
            >
            <a href="tel:+15551234567" class="btn btn-secondary"
              ><i class="fas fa-phone-alt"></i> Call Us Now</a
            >
          </div>
          <div class="trust-indicators">
            <div class="trust-item">
              <i class="fas fa-check-circle"></i>
              <span>Free Consultation</span>
            </div>
            <div class="trust-item">
              <i class="fas fa-check-circle"></i>
              <span>Expert Advisors</span>
            </div>
            <div class="trust-item">
              <i class="fas fa-check-circle"></i>
              <span>Personalized Plans</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer Section -->
    <footer>
      <div class="container">
        <div class="footer-content">
          <div class="footer-logo">
            <div class="footer-logo-img">
              <img
                src="./images/growwell_logo_01.webp"
                alt="Growwell Logo"
                loading="lazy"
                class="footer-logo-image"
              />
            </div>
            <p>Your partner for financial growth and security</p>
            <div class="social-icons">
              <a
                href="https://www.facebook.com/growwellimf/"
                target="/"
                aria-label="Follow us on Facebook"
                ><i class="fab fa-facebook-f"></i
              ></a>
              <a
                href="https://x.com/Growwell__?t=QZcsQv6QemM2qT4bfejZeg&s=08"
                target="/"
                aria-label="Follow us on X"
                ><i class="fab fa-twitter"></i
              ></a>
              <a
                href="https://www.linkedin.com/company/grow-well-imf/about/"
                target="/"
                aria-label="Follow us on Linkedin"
                ><i class="fab fa-linkedin-in"></i
              ></a>
              <a
                href="https://www.instagram.com/growwell.imf/"
                target="/"
                aria-label="Follow us on Instagram"
                ><i class="fab fa-instagram"></i
              ></a>
            </div>
          </div>
          <div class="footer-links">
            <div class="footer-column">
              <h3>Quick Links</h3>
              <ul>
                <li><a href="index.html">Home</a></li>
                <li><a href="./investment-plans.html">Products</a></li>
                <li><a href="./renew-policy.html">Renew</a></li>
                <li><a href="./advisor.html">Testimonials</a></li>
                <li><a href="./contact.html">Contact</a></li>
              </ul>
            </div>
            <div class="footer-column">
              <h3>Services</h3>
              <ul>
                <li><a href="investment-plans.html">Financial Planning</a></li>
                <li><a href="child-plans.html">Child Planning</a></li>
                <li><a href="investment-plans.html">Investment Planning</a></li>
                <li><a href="retirement-plans.html">Retirement Planning</a></li>
                <li><a href="term-insurance.html">Term Insurance</a></li>
                <li><a href="#">Tax Advisory</a></li>
              </ul>
            </div>
            <div class="footer-column">
              <h3>Resources</h3>
              <ul>
                <li><a href="leadership.html">Leadership</a></li>
                <li><a href="advisor.html">Advisor</a></li>
                <li>
                  <a href="retirement-plans.html">Financial Calculators</a>
                </li>
                <li><a href="faqs.html">FAQs</a></li>
                <li><a href="contact.html">contact</a></li>
              </ul>
            </div>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2023 Growwell Financial Advisors. All rights reserved.</p>
          <ul class="footer-legal">
            <li><a href="#">Privacy Policy</a></li>
            <li><a href="#">Terms of Service</a></li>
            <li><a href="#">Cookie Policy</a></li>
          </ul>
        </div>
      </div>
    </footer>

    <!-- Plan Details Modal -->
    <div id="planModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h2 id="modalTitle">Plan Details</h2>
          <span class="close">&times;</span>
        </div>
        <div class="modal-body">
          <div class="plan-details-container">
            <div class="plan-info-section">
              <div class="plan-badge-modal" id="modalBadge">Physical</div>
              <h3 id="modalPlanName">Growth Essentials</h3>
              <div class="price-modal" id="modalPrice">
                <span class="currency">₹</span>
                <span class="amount">29</span>
                <span class="period">/month</span>
              </div>
            </div>

            <div class="plan-features-section">
              <h4>Plan Features</h4>
              <ul id="modalFeatures" class="modal-features-list">
                <!-- Features will be populated by JavaScript -->
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- JS Scripts -->
    <script src="js/main.js"></script>
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "Growwell",
        "url": "https://growwellimf.com",
        "logo": "https://growwellimf.com/images/growwell_logo_01.webp",
        "sameAs": [
          "https://www.facebook.com/growwellimf/",
          "https://www.instagram.com/growwell.imf/",
          "https://x.com/Growwell__?t=QZcsQv6QemM2qT4bfejZeg&s=08",
          "https://www.linkedin.com/company/grow-well-imf/about/"
        ]
      }
    </script>
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Mobile Menu Toggle
        const hamburger = document.getElementById("hamburger");
        const navMenu = document.getElementById("nav-menu");
        const body = document.body;

        if (hamburger) {
          hamburger.addEventListener("click", function () {
            navMenu.classList.toggle("active");
            body.classList.toggle("nav-active");

            // Toggle hamburger appearance
            const bars = this.querySelectorAll(".bar");
            bars.forEach((bar) => bar.classList.toggle("active"));
          });
        }

        // Dropdown Toggle for Mobile
        const dropdownToggles = document.querySelectorAll(".dropdown-toggle");
        dropdownToggles.forEach((toggle) => {
          toggle.addEventListener("click", function (e) {
            // Only run on mobile (width <= 992px)
            if (window.innerWidth <= 992) {
              e.preventDefault();

              // Close other open dropdowns
              document.querySelectorAll(".dropdown-menu").forEach((menu) => {
                if (menu !== this.nextElementSibling)
                  menu.classList.remove("active");
              });
              document.querySelectorAll(".dropdown-toggle").forEach((tog) => {
                if (tog !== this) tog.classList.remove("active");
              });

              // Toggle this dropdown
              this.nextElementSibling.classList.toggle("active");
              this.classList.toggle("active");
            }
          });
        });

        // Optional: Close menu when clicking outside
        document.addEventListener("click", function (e) {
          if (window.innerWidth <= 992) {
            if (!e.target.closest("nav") && !e.target.closest("#hamburger")) {
              navMenu.classList.remove("active");
              body.classList.remove("nav-active");
              document
                .querySelectorAll(".dropdown-menu")
                .forEach((menu) => menu.classList.remove("active"));
              document
                .querySelectorAll(".dropdown-toggle")
                .forEach((tog) => tog.classList.remove("active"));

              // Reset hamburger bars
              const bars = hamburger.querySelectorAll(".bar");
              bars.forEach((bar) => bar.classList.remove("active"));
            }
          }
        });

        // Simple FAQ Toggle - Direct Implementation
        document.querySelectorAll(".faq-question").forEach((question) => {
          question.addEventListener("click", function () {
            const item = this.parentNode;

            // Toggle active class on this FAQ item
            item.classList.toggle("active");

            // Get the answer element
            const answer = item.querySelector(".faq-answer");

            // Toggle display on answer
            if (answer) {
              if (answer.style.maxHeight) {
                answer.style.maxHeight = null;
                answer.style.paddingTop = "0";
                answer.style.paddingBottom = "0";
              } else {
                answer.style.maxHeight = answer.scrollHeight + 40 + "px";
                answer.style.paddingTop = "10px";
                answer.style.paddingBottom = "20px";
              }
            }

            // Rotate icon for this question
            const icon = this.querySelector("i");
            if (icon) {
              icon.style.transform = item.classList.contains("active")
                ? "rotate(180deg)"
                : "rotate(0)";
            }

            // Close other FAQ items (optional)
            document.querySelectorAll(".faq-item").forEach((otherItem) => {
              if (
                otherItem !== item &&
                otherItem.classList.contains("active")
              ) {
                otherItem.classList.remove("active");

                const otherAnswer = otherItem.querySelector(".faq-answer");
                if (otherAnswer) {
                  otherAnswer.style.maxHeight = null;
                  otherAnswer.style.paddingTop = "0";
                  otherAnswer.style.paddingBottom = "0";
                }

                const otherIcon = otherItem.querySelector(".faq-question i");
                if (otherIcon) {
                  otherIcon.style.transform = "rotate(0)";
                }
              }
            });
          });
        });

        // Open first FAQ item by default
        const firstItem = document.querySelector(".faq-item");
        if (firstItem) {
          firstItem.classList.add("active");
          const firstAnswer = firstItem.querySelector(".faq-answer");
          if (firstAnswer) {
            firstAnswer.style.maxHeight = firstAnswer.scrollHeight + 40 + "px";
            firstAnswer.style.paddingTop = "10px";
            firstAnswer.style.paddingBottom = "20px";
          }
          const firstIcon = firstItem.querySelector(".faq-question i");
          if (firstIcon) {
            firstIcon.style.transform = "rotate(180deg)";
          }
        }

        // Feature icon animations
        document.querySelectorAll(".feature-link").forEach((link) => {
          link.addEventListener("mouseenter", function () {
            const icon = this.querySelector("i");
            if (icon) icon.style.transform = "translateX(5px)";
          });

          link.addEventListener("mouseleave", function () {
            const icon = this.querySelector("i");
            if (icon) icon.style.transform = "";
          });
        });

        // Animate feature cards on scroll
        const animateOnScroll = function (entries, observer) {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              entry.target.classList.add("animate");
              observer.unobserve(entry.target);
            }
          });
        };

        const options = {
          threshold: 0.2,
          rootMargin: "0px 0px -100px 0px",
        };

        const observer = new IntersectionObserver(animateOnScroll, options);

        // Observe feature cards
        document.querySelectorAll(".feature-card").forEach((card, index) => {
          card.style.opacity = "0";
          card.style.transform = "translateY(30px)";
          card.style.transition = `all 0.6s ease ${index * 0.1}s`;
          observer.observe(card);
        });

        // Plan Tabs Functionality
        const planTabs = document.querySelectorAll(".plan-tab");
        const planCards = document.querySelectorAll(".plan-card");

        planTabs.forEach((tab) => {
          tab.addEventListener("click", function () {
            // Remove active class from all tabs
            planTabs.forEach((t) => t.classList.remove("active"));

            // Add active class to clicked tab
            this.classList.add("active");

            // Get selected category
            const filter = this.getAttribute("data-filter");

            // Filter plan cards based on category
            planCards.forEach((card) => {
              if (
                filter === "all" ||
                card.getAttribute("data-category").includes(filter)
              ) {
                card.style.display = "block";
                // Add animation
                card.classList.add("animate-in");
                setTimeout(() => {
                  card.classList.remove("animate-in");
                }, 500);
              } else {
                card.style.display = "none";
              }
            });
          });
        });

        // Plan Modal Functionality
        const modal = document.getElementById("planModal");
        const closeBtn = document.querySelector(".close");
        let currentPlanId = null;

        // Plan data
        const planData = {
          'growth-essentials': {
            name: 'Growth Essentials',
            price: 29,
            category: 'physical',
            badge: 'Physical',
            features: [
              'Personalized nutrition plans',
              'Age-appropriate exercise routines',
              'Growth tracking tools',
              'Monthly consultation',
              'Basic health resources'
            ]
          },
          'brain-builders': {
            name: 'Brain Builders',
            price: 49,
            category: 'cognitive',
            badge: 'Cognitive',
            features: [
              'Customized learning activities',
              'Critical thinking exercises',
              'Weekly progress reports',
              'Educational resources library',
              'Bi-weekly expert consultation'
            ]
          },
          'emotional-intelligence': {
            name: 'Emotional Intelligence',
            price: 39,
            category: 'emotional',
            badge: 'Emotional',
            features: [
              'Emotion recognition tools',
              'Social skills development',
              'Stress management techniques',
              'Monthly parent guidance',
              'Child-friendly mindfulness activities'
            ]
          },
          'complete-development': {
            name: 'Complete Development',
            price: 79,
            category: 'physical cognitive',
            badge: 'Physical Cognitive',
            features: [
              'All features from Growth Essentials',
              'All features from Brain Builders',
              'Weekly expert consultation',
              'Priority support',
              'Advanced progress analytics'
            ]
          },
          'balanced-growth': {
            name: 'Balanced Growth',
            price: 69,
            category: 'physical emotional',
            badge: 'Physical Emotional',
            features: [
              'All features from Growth Essentials',
              'All features from Emotional Intelligence',
              'Weekly expert consultation',
              'Family activities guide',
              'Quarterly in-depth assessment'
            ]
          },
          'comprehensive-care': {
            name: 'Comprehensive Care',
            price: 99,
            category: 'physical cognitive emotional',
            badge: 'Physical Cognitive Emotional',
            features: [
              'All features from all other plans',
              'Personalized development roadmap',
              'Unlimited expert consultations',
              'VIP support 24/7',
              'Exclusive workshops and webinars'
            ]
          }
        };

        // Modal functions
        window.openPlanModal = function(planId) {
          currentPlanId = planId;
          const plan = planData[planId];

          if (plan) {
            // Populate modal with plan data
            document.getElementById('modalTitle').textContent = plan.name + ' - Details';
            document.getElementById('modalBadge').textContent = plan.badge;
            document.getElementById('modalPlanName').textContent = plan.name;
            document.querySelector('#modalPrice .amount').textContent = plan.price;

            // Populate features
            const featuresList = document.getElementById('modalFeatures');
            featuresList.innerHTML = '';
            plan.features.forEach(feature => {
              const li = document.createElement('li');
              li.innerHTML = '<i class="fas fa-check"></i> ' + feature;
              featuresList.appendChild(li);
            });

            // Show modal
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
          }
        };

        window.closeModal = function() {
          modal.style.display = 'none';
          document.body.style.overflow = 'auto';
          currentPlanId = null;
        };

        // Close modal when clicking on close button
        closeBtn.addEventListener('click', closeModal);

        // Close modal when clicking outside of it
        window.addEventListener('click', function(event) {
          if (event.target === modal) {
            closeModal();
          }
        });


      });
    </script>

    <style>
      /* Comprehensive Direct Inline CSS fixes for FAQ section */
      .faq-section {
        padding: 100px 0;
        background-color: #f8f9fb;
        position: relative;
      }

      .faq-content {
        max-width: 800px;
        margin: 50px auto 0;
      }

      .faq-item {
        background: white;
        border-radius: 10px;
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        overflow: hidden;
        transition: all 0.3s ease;
        position: relative;
        border: 1px solid #eee;
      }

      .faq-item:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
      }

      .faq-item.active {
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border-color: rgba(76, 175, 80, 0.3);
      }

      .faq-question {
        padding: 20px 25px;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: 600;
        color: #333;
        font-size: 18px;
        position: relative;
        transition: all 0.3s ease;
      }

      .faq-question span {
        flex: 1;
      }

      .faq-item.active .faq-question {
        color: var(--accent-color);
        background-color: rgba(76, 175, 80, 0.05);
      }

      .faq-question i {
        color: var(--accent-color);
        transition: transform 0.3s ease;
        font-size: 16px;
        margin-left: 15px;
        min-width: 16px;
        text-align: center;
      }

      .faq-answer {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.4s ease, padding 0.4s ease;
        padding: 0 25px;
        color: #666;
        line-height: 1.7;
      }

      .faq-answer p {
        margin: 0;
        line-height: 1.7;
        color: #666;
      }

      /* Make sure the FAQ section loads properly on page load */
      .faq-item:first-child .faq-question i {
        transform: rotate(180deg);
      }

      /* Media queries for better mobile experience */
      @media (max-width: 768px) {
        .faq-section {
          padding: 70px 0;
        }

        .faq-content {
          margin-top: 30px;
          padding: 0 15px;
        }

        .faq-question {
          font-size: 16px;
          padding: 18px 20px;
        }
      }

      @media (max-width: 576px) {
        .faq-section {
          padding: 50px 0;
        }

        .faq-question {
          font-size: 15px;
          padding: 15px;
        }

        .faq-answer {
          padding: 0 15px;
          font-size: 14px;
        }

        .faq-answer p {
          padding-bottom: 15px;
        }

        .faq-item {
          margin-bottom: 10px;
        }
      }
    </style>
  </body>
</html>
