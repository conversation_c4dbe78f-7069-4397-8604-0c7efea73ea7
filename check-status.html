<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Check Your Policy Status | Growwell Insurance</title>
    <meta
      name="description"
      content="Renew your financial services or investment plans with Growwell. Continue your journey toward financial security with seamless plan renewal options."
    />
    <meta
      name="keywords"
      content="growwell, financial services, investment plans, financial security"
    />
    <meta name="robots" content="check-status, follow" />
    <link rel="canonical" href="https://growwellimf.com/" />
    <meta
      property="og:title"
      content="Check Your Policy Status | Growwell Insurance"
    />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://growwellimf.com/" />
    <meta
      property="og:image"
      content="https://growwellimf.com/images/growwell_logo_01.webp"
    />
    <meta name="author" content="Growwell" />
    <meta name="publisher" content="Growwell" />

    <!--google adsense-->
    <meta name="google-adsense-account" content="ca-pub-****************" />
    <script
      async
      src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************"
      crossorigin="anonymous"
    ></script>

    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="css/check-status.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <!-- Header Section (Same as other pages) -->
    <header>
      <div class="container">
        <div class="navbar">
          <div class="logo">
            <a href="index.html">
              <img
                src="./images/growwell_logo_01.webp"
                alt="company_logo"
                class="brand-logo"
            /></a>
            <a href="index.html"
              ><h2><span class="highlight">Grow</span>well</h2></a
            >
          </div>

          <div class="hamburger" id="hamburger">
            <div class="bar"></div>
            <div class="bar"></div>
            <div class="bar"></div>
          </div>

          <nav id="nav-menu">
            <ul id="menu-items">
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Products</a>
                <div class="dropdown-menu">
                  <a href="investment-plans.html">Investment Plans</a>
                  <a href="retirement-plans.html">Retirement Plans</a>
                  <a href="child-plans.html">Child Plans</a>
                  <a href="term-insurance.html">Term Insurance</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Renew</a>
                <div class="dropdown-menu">
                  <a href="renew-policy.html">Renew Policy</a>
                  <a href="check-status.html">Check Status</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Claim</a>
                <div class="dropdown-menu">
                  <a href="file-claim.html">File a Claim</a>
                  <a href="track-claim.html">Track Claim</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Help & Support</a>
                <div class="dropdown-menu">
                  <a href="faqs.html">FAQs</a>
                  <a href="contact.html">Contact Us</a>
                  <a href="support.html">Customer Support</a>
                  <a href="advisor.html">Find an Advisor</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">About Us</a>
                <div class="dropdown-menu">
                  <a href="our-story.html">Our Story</a>
                  <a href="leadership.html">Leadership</a>
                  <a href="careers.html">Careers</a>
                </div>
              </li>
            </ul>
          </nav>

          <div class="navbar-actions desktop-only">
            <a href="investment-plans.html" class="btn-primary">Get Started</a>
          </div>
        </div>
      </div>
    </header>

    <!-- Hero Section -->
    <section class="status-hero">
      <div class="hero-particles"></div>
      <div class="container">
        <div class="status-hero-content">
          <div class="hero-badge">
            <i class="fas fa-shield-alt"></i>
            Track & Monitor
          </div>
          <h1>
            Track Your Policy Status <span class="highlight">in Real-Time</span>
          </h1>
          <p>
            Get instant updates on your policy application, renewal status, and
            claims - all in one place.
          </p>

          <div class="status-search-container">
            <div class="search-type-selector">
              <div class="selector-item active" data-tab="application">
                <div class="selector-icon">
                  <i class="fas fa-file-alt"></i>
                </div>
                <span>Application</span>
              </div>
              <div class="selector-item" data-tab="renewal">
                <div class="selector-icon">
                  <i class="fas fa-sync-alt"></i>
                </div>
                <span>Renewal</span>
              </div>
              <div class="selector-item" data-tab="claim">
                <div class="selector-icon">
                  <i class="fas fa-clipboard-check"></i>
                </div>
                <span>Claim</span>
              </div>
            </div>

            <div class="search-box-wrapper">
              <div class="search-input-group">
                <div class="input-wrapper">
                  <div class="input-icon">
                    <i class="fas fa-search"></i>
                  </div>
                  <input
                    type="text"
                    placeholder="Enter your reference number"
                    class="status-search-input"
                  />
                </div>
                <button class="status-search-btn">
                  Track Status
                  <i class="fas fa-arrow-right"></i>
                </button>
              </div>
              <div class="search-features">
                <button class="feature-item">
                  <i class="fas fa-history"></i>
                  <span>Recent Searches</span>
                </button>
                <button class="feature-item">
                  <i class="fas fa-question-circle"></i>
                  <span>Find Reference Number</span>
                </button>
                <button class="feature-item">
                  <i class="fas fa-headset"></i>
                  <span>Need Help?</span>
                </button>
              </div>
            </div>
          </div>

          <div class="hero-stats">
            <div class="stat-item">
              <div class="stat-icon">
                <i class="fas fa-bolt"></i>
              </div>
              <div class="stat-info">
                <div class="stat-number">2s</div>
                <p>Average Track Time</p>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon">
                <i class="fas fa-shield-alt"></i>
              </div>
              <div class="stat-info">
                <div class="stat-number">256-bit</div>
                <p>Secure Encryption</p>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon">
                <i class="fas fa-users"></i>
              </div>
              <div class="stat-info">
                <div class="stat-number">50K+</div>
                <p>Happy Customers</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="hero-shape"></div>
    </section>

    <!-- Status Timeline Section -->
    <section class="status-timeline-section">
      <div class="container">
        <div class="status-overview-card">
          <div class="overview-header">
            <div class="overview-type">
              <div class="overview-icon">
                <i class="fas fa-file-alt"></i>
              </div>
              <div class="overview-details">
                <span class="overview-label">Policy Application</span>
                <h2>Application Status Overview</h2>
              </div>
            </div>
            <div class="overview-meta">
              <div class="meta-item">
                <i class="fas fa-hashtag meta-icon"></i>
                <div class="meta-content">
                  <span class="meta-label">Reference</span>
                  <span class="meta-value">GW-2023-1234567</span>
                </div>
              </div>
              <div class="meta-item">
                <i class="fas fa-clock meta-icon"></i>
                <div class="meta-content">
                  <span class="meta-label">Last Updated</span>
                  <span class="meta-value">2 hours ago</span>
                </div>
              </div>
              <div class="meta-item">
                <i class="fas fa-calendar meta-icon"></i>
                <div class="meta-content">
                  <span class="meta-label">Submitted On</span>
                  <span class="meta-value">Oct 15, 2023</span>
                </div>
              </div>
            </div>
          </div>

          <div class="overview-actions">
            <button class="action-btn primary">
              <i class="fas fa-download"></i> Download PDF
            </button>
            <button class="action-btn secondary">
              <i class="fas fa-print"></i> Print
            </button>
            <button class="action-btn secondary">
              <i class="fas fa-share-alt"></i> Share
            </button>
          </div>
        </div>

        <div class="progress-wrapper">
          <div class="progress-info">
            <span class="progress-label">Overall Progress</span>
            <span class="progress-percentage">75%</span>
          </div>
          <div class="progress-bar">
            <div class="progress-fill" style="width: 75%"></div>
          </div>
        </div>

        <div class="status-steps">
          <div class="status-step completed">
            <div class="step-indicator">
              <div class="step-circle">
                <i class="fas fa-file-alt"></i>
              </div>
              <div class="step-line"></div>
            </div>
            <div class="step-content">
              <div class="step-header">
                <h3>Application Submitted</h3>
                <span class="status-tag completed">Completed</span>
              </div>
              <p>
                Your application has been successfully received and registered
                in our system.
              </p>
              <div class="step-footer">
                <span class="step-date"
                  ><i class="fas fa-calendar-alt"></i> Oct 15, 2023 - 10:30
                  AM</span
                >
                <a href="#" class="step-link">View Details</a>
              </div>
            </div>
          </div>

          <div class="status-step completed">
            <div class="step-indicator">
              <div class="step-circle">
                <i class="fas fa-check-circle"></i>
              </div>
              <div class="step-line"></div>
            </div>
            <div class="step-content">
              <div class="step-header">
                <h3>Document Verification</h3>
                <span class="status-tag completed">Completed</span>
              </div>
              <p>All submitted documents have been verified and approved.</p>
              <div class="step-footer">
                <span class="step-date"
                  ><i class="fas fa-calendar-alt"></i> Oct 16, 2023 - 2:45
                  PM</span
                >
                <a href="#" class="step-link">View Documents</a>
              </div>
            </div>
          </div>

          <div class="status-step active">
            <div class="step-indicator">
              <div class="step-circle">
                <i class="fas fa-user-check"></i>
              </div>
              <div class="step-line"></div>
            </div>
            <div class="step-content">
              <div class="step-header">
                <h3>Underwriting Review</h3>
                <span class="status-tag active">In Progress</span>
              </div>
              <p>
                Your application is being reviewed by our underwriting team.
              </p>
              <div class="step-footer">
                <span class="step-date"
                  ><i class="fas fa-clock"></i> Estimated completion: 24-48
                  hours</span
                >
              </div>
            </div>
          </div>

          <div class="status-step">
            <div class="step-indicator">
              <div class="step-circle">
                <i class="fas fa-file-signature"></i>
              </div>
            </div>
            <div class="step-content">
              <div class="step-header">
                <h3>Policy Issuance</h3>
                <span class="status-tag pending">Pending</span>
              </div>
              <p>Final step - Policy document generation and delivery.</p>
              <div class="step-footer">
                <span class="step-date"
                  ><i class="fas fa-clock"></i> Awaiting previous step
                  completion</span
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Quick Actions Section -->
    <section class="quick-actions-section">
      <div class="container">
        <div class="section-header">
          <h2>Need Help?</h2>
          <p class="section-subtitle">
            We're here to assist you every step of the way
          </p>
        </div>
        <div class="actions-grid">
          <div class="action-card">
            <div class="action-icon">
              <i class="fas fa-headset"></i>
            </div>
            <h3>Talk to Support</h3>
            <p>
              Get instant help from our support team for any queries related to
              your policy status
            </p>
            <a href="#" class="action-button">
              Contact Support
              <i class="fas fa-arrow-right"></i>
            </a>
          </div>
          <div class="action-card">
            <div class="action-icon">
              <i class="fas fa-file-download"></i>
            </div>
            <h3>Download Documents</h3>
            <p>
              Access and download all your policy related documents securely
              from our portal
            </p>
            <a href="#" class="action-button">
              View Documents
              <i class="fas fa-arrow-right"></i>
            </a>
          </div>
          <div class="action-card">
            <div class="action-icon">
              <i class="fas fa-calendar-alt"></i>
            </div>
            <h3>Schedule a Call</h3>
            <p>
              Book a convenient time slot for a detailed discussion with our
              insurance expert
            </p>
            <a href="#" class="action-button">
              Book Appointment
              <i class="fas fa-arrow-right"></i>
            </a>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section class="status-faq-section">
      <div class="container">
        <div class="section-header">
          <h2>Frequently Asked <span class="check-txt"> Questions</span></h2>
          <p class="section-subtitle">
            Quick answers to common queries about policy status tracking
          </p>
        </div>
        <div class="faq-grid">
          <div class="faq-item">
            <div class="faq-question">
              <h3>How can I track my policy status?</h3>
              <div class="faq-icon">
                <i class="fas fa-chevron-down"></i>
              </div>
            </div>
            <div class="faq-answer">
              <p>
                You can track your policy status by entering your reference
                number in the search box above. The reference number can be
                found in your acknowledgment email or SMS. Alternatively, you
                can also track using your mobile number or email ID registered
                with us.
              </p>
            </div>
          </div>
          <div class="faq-item">
            <div class="faq-question">
              <h3>What if I lost my reference number?</h3>
              <div class="faq-icon">
                <i class="fas fa-chevron-down"></i>
              </div>
            </div>
            <div class="faq-answer">
              <p>
                Don't worry! You can retrieve your reference number by
                contacting our support team or checking your registered email
                inbox for the acknowledgment message. You can also use the "Find
                Reference Number" option on our tracking page by providing your
                registered email or mobile number.
              </p>
            </div>
          </div>
          <div class="faq-item">
            <div class="faq-question">
              <h3>How long does the application process take?</h3>
              <div class="faq-icon">
                <i class="fas fa-chevron-down"></i>
              </div>
            </div>
            <div class="faq-answer">
              <p>
                Typically, the application process takes 3-5 business days.
                However, this may vary depending on the type of policy and the
                completeness of submitted documents. You can always check the
                current status and estimated completion time through our
                tracking system.
              </p>
            </div>
          </div>
          <div class="faq-item">
            <div class="faq-question">
              <h3>Can I track multiple applications?</h3>
              <div class="faq-icon">
                <i class="fas fa-chevron-down"></i>
              </div>
            </div>
            <div class="faq-answer">
              <p>
                Yes, you can track multiple applications by entering their
                respective reference numbers. Your recent searches will be saved
                for quick access, making it convenient to check multiple
                policies in a single session.
              </p>
            </div>
          </div>
        </div>
        <div class="faq-cta">
          <p>Have more questions?</p>
          <a href="#" class="btn-secondary">View All FAQs</a>
        </div>
      </div>
    </section>

    <!-- Footer Section -->
    <footer>
      <div class="container">
        <div class="footer-content">
          <div class="footer-logo">
            <div class="footer-logo-img">
              <img
                src="./images/growwell_logo_01.webp"
                alt="Growwell Logo"
                loading="lazy"
                class="footer-logo-image"
              />
            </div>
            <p>Your partner for financial growth and security</p>
            <div class="social-icons">
              <a
                href="https://www.facebook.com/growwellimf/"
                target="/"
                aria-label="Follow us on Facebook"
                ><i class="fab fa-facebook-f"></i
              ></a>
              <a
                href="https://x.com/Growwell__?t=QZcsQv6QemM2qT4bfejZeg&s=08"
                target="/"
                aria-label="Follow us on X"
                ><i class="fab fa-twitter"></i
              ></a>
              <a
                href="https://www.linkedin.com/company/grow-well-imf/about/"
                target="/"
                aria-label="Follow us on Linkedin"
                ><i class="fab fa-linkedin-in"></i
              ></a>
              <a
                href="https://www.instagram.com/growwell.imf/"
                target="/"
                aria-label="Follow us on Instagram"
                ><i class="fab fa-instagram"></i
              ></a>
            </div>
          </div>
          <div class="footer-links">
            <div class="footer-column">
              <h3>Quick Links</h3>
              <ul>
                <li><a href="index.html">Home</a></li>
                <li><a href="./investment-plans.html">Products</a></li>
                <li><a href="./renew-policy.html">Renew</a></li>
                <li><a href="./advisor.html">Testimonials</a></li>
                <li><a href="./contact.html">Contact</a></li>
              </ul>
            </div>
            <div class="footer-column">
              <h3>Services</h3>
              <ul>
                <li><a href="investment-plans.html">Financial Planning</a></li>
                <li><a href="child-plans.html">Child Planning</a></li>
                <li><a href="investment-plans.html">Investment Planning</a></li>
                <li><a href="retirement-plans.html">Retirement Planning</a></li>
                <li><a href="term-insurance.html">Term Insurance</a></li>
                <li><a href="#">Tax Advisory</a></li>
              </ul>
            </div>
            <div class="footer-column">
              <h3>Resources</h3>
              <ul>
                <li><a href="leadership.html">Leadership</a></li>
                <li><a href="advisor.html">Advisor</a></li>
                <li>
                  <a href="retirement-plans.html">Financial Calculators</a>
                </li>
                <li><a href="faqs.html">FAQs</a></li>
                <li><a href="contact.html">contact</a></li>
              </ul>
            </div>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2023 Growwell Financial Advisors. All rights reserved.</p>
          <ul class="footer-legal">
            <li><a href="#">Privacy Policy</a></li>
            <li><a href="#">Terms of Service</a></li>
            <li><a href="#">Cookie Policy</a></li>
          </ul>
        </div>
      </div>
    </footer>

    <script src="js/check-status.js"></script>
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "Growwell",
        "url": "https://growwellimf.com",
        "logo": "https://growwellimf.com/images/growwell_logo_01.webp",
        "sameAs": [
          "https://www.facebook.com/growwellimf/",
          "https://www.instagram.com/growwell.imf/",
          "https://x.com/Growwell__?t=QZcsQv6QemM2qT4bfejZeg&s=08",
          "https://www.linkedin.com/company/grow-well-imf/about/"
        ]
      }
    </script>
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Mobile Menu Toggle
        const hamburger = document.getElementById("hamburger");
        const navMenu = document.getElementById("nav-menu");
        const body = document.body;

        if (hamburger) {
          hamburger.addEventListener("click", function () {
            navMenu.classList.toggle("active");
            body.classList.toggle("nav-active");

            // Toggle hamburger appearance
            const bars = this.querySelectorAll(".bar");
            bars.forEach((bar) => bar.classList.toggle("active"));

            console.log(
              "Hamburger clicked. Nav active:",
              navMenu.classList.contains("active")
            );
          });
        }

        // Dropdown Toggle for Mobile
        const dropdownToggles = document.querySelectorAll(".dropdown-toggle");
        dropdownToggles.forEach((toggle) => {
          toggle.addEventListener("click", function (e) {
            // Only run on mobile (width <= 992px)
            if (window.innerWidth <= 992) {
              e.preventDefault();
              e.stopPropagation();

              // Find the dropdown menu that is a sibling of this toggle
              const dropdownMenu = this.nextElementSibling;

              // Close all other dropdown menus
              document.querySelectorAll(".dropdown-menu").forEach((menu) => {
                if (menu !== dropdownMenu) {
                  menu.classList.remove("active");
                }
              });

              // Remove active class from all other toggles
              document.querySelectorAll(".dropdown-toggle").forEach((tog) => {
                if (tog !== this) {
                  tog.classList.remove("active");
                }
              });

              // Toggle active class on this dropdown menu and toggle
              dropdownMenu.classList.toggle("active");
              this.classList.toggle("active");
            }
          });
        });

        // Close menu when clicking outside
        document.addEventListener("click", function (e) {
          if (window.innerWidth <= 992) {
            if (!e.target.closest("nav") && !e.target.closest("#hamburger")) {
              // Close the navigation menu
              if (navMenu.classList.contains("active")) {
                navMenu.classList.remove("active");
                body.classList.remove("nav-active");

                // Reset hamburger icon
                const bars = hamburger.querySelectorAll(".bar");
                bars.forEach((bar) => bar.classList.remove("active"));

                // Close all dropdown menus
                document.querySelectorAll(".dropdown-menu").forEach((menu) => {
                  menu.classList.remove("active");
                });

                // Remove active class from all toggles
                document.querySelectorAll(".dropdown-toggle").forEach((tog) => {
                  tog.classList.remove("active");
                });
              }
            }
          }
        });

        // Initialize status section tab functionality
        const selectorItems = document.querySelectorAll(".selector-item");
        selectorItems.forEach((item) => {
          item.addEventListener("click", function () {
            // Remove active class from all tabs
            selectorItems.forEach((tab) => tab.classList.remove("active"));

            // Add active class to clicked tab
            this.classList.add("active");

            // You can add logic here to show different content based on tab
            const tabType = this.getAttribute("data-tab");
            console.log("Tab selected:", tabType);
          });
        });

        // FAQ accordion functionality
        const faqItems = document.querySelectorAll(".faq-item");
        faqItems.forEach((item) => {
          const question = item.querySelector(".faq-question");
          question.addEventListener("click", function () {
            item.classList.toggle("active");
          });
        });
      });
    </script>
  </body>
</html>
