<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Customer Support | Financial Help | Growwell</title>
    <meta
      name="description"
      content="Get in touch with Growwell financial experts today. We’re here to answer your questions and help you plan your financial future with confidence."
    />
    <meta
      name="keywords"
      content="growwell, financial experts, financial help, financial future"
    />
    <meta name="robots" content="support, follow" />
    <link rel="canonical" href="https://growwellimf.com/" />
    <meta
      property="og:title"
      content="Customer Support | Financial Help | Growwell"
    />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://growwellimf.com/" />
    <meta
      property="og:image"
      content="https://growwellimf.com/images/growwell_logo_01.webp"
    />
    <meta name="author" content="Growwell" />
    <meta name="publisher" content="Growwell" />

    <!--google adsense-->
    <meta name="google-adsense-account" content="ca-pub-****************" />
    <script
      async
      src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************"
      crossorigin="anonymous"
    ></script>

    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="css/support.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <!-- Header Section -->
    <header>
      <div class="container">
        <div class="navbar">
          <div class="logo">
            <a href="index.html">
              <img
                src="./images/growwell_logo_01.webp"
                alt="company_logo"
                class="brand-logo"
            /></a>
            <a href="index.html"
              ><h2><span class="highlight">Grow</span>well</h2></a
            >
          </div>

          <div class="hamburger" id="hamburger">
            <div class="bar"></div>
            <div class="bar"></div>
            <div class="bar"></div>
          </div>

          <nav id="nav-menu">
            <ul id="menu-items">
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Products</a>
                <div class="dropdown-menu">
                  <a href="investment-plans.html">Investment Plans</a>
                  <a href="retirement-plans.html">Retirement Plans</a>
                  <a href="child-plans.html">Child Plans</a>
                  <a href="term-insurance.html">Term Insurance</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Renew</a>
                <div class="dropdown-menu">
                  <a href="renew-policy.html">Renew Policy</a>
                  <a href="check-status.html">Check Status</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Claim</a>
                <div class="dropdown-menu">
                  <a href="file-claim.html">File a Claim</a>
                  <a href="track-claim.html">Track Claim</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">Help & Support</a>
                <div class="dropdown-menu">
                  <a href="faqs.html">FAQs</a>
                  <a href="contact.html">Contact Us</a>
                  <a href="support.html">Customer Support</a>
                  <a href="advisor.html">Find an Advisor</a>
                </div>
              </li>
              <li class="dropdown">
                <a href="#" class="dropdown-toggle">About Us</a>
                <div class="dropdown-menu">
                  <a href="our-story.html">Our Story</a>
                  <a href="leadership.html">Leadership</a>
                  <a href="careers.html">Careers</a>
                </div>
              </li>
            </ul>
          </nav>

          <div class="navbar-actions desktop-only">
            <a href="investment-plans.html" class="btn-primary">Get Started</a>
          </div>
        </div>
      </div>
    </header>

    <!-- Support Hero Section -->
    <section class="support-hero">
      <div class="container">
        <div class="support-hero-content">
          <h1>Customer Support</h1>
          <p>
            We're here to help you with any questions or issues you may have.
          </p>
        </div>
      </div>
    </section>

    <!-- Support Main Section -->
    <section class="support-section">
      <div class="container">
        <div class="support-container">
          <!-- Support Options -->
          <div class="section-header">
            <span class="section-subtitle">How Can We Help?</span>
            <h2>
              Choose Your <span class="support-txt"> Support Option</span>
            </h2>
            <p>Select the support channel that works best for you</p>
          </div>

          <div class="support-options">
            <div class="support-option">
              <div class="support-icon">
                <i class="fas fa-headset"></i>
              </div>
              <h3>Call Us</h3>
              <p>
                Speak directly with our customer support team for immediate
                assistance with your queries.
              </p>
              <p><strong>Toll Free:</strong> 1800 123 456</p>
              <p><strong>Hours:</strong> Mon-Fri, 9AM-6PM</p>
              <a href="tel:+911800123456" class="btn-primary">Call Now</a>
            </div>

            <div class="support-option">
              <div class="support-icon">
                <i class="fas fa-comments"></i>
              </div>
              <h3>Live Chat</h3>
              <p>
                Chat with our support representatives in real-time for quick
                answers to your questions.
              </p>
              <p><strong>Response Time:</strong> Under 2 minutes</p>
              <p><strong>Hours:</strong> 24/7 Support</p>
              <a href="#" class="btn-primary">Start Chat</a>
            </div>

            <div class="support-option">
              <div class="support-icon">
                <i class="fas fa-envelope"></i>
              </div>
              <h3>Email Support</h3>
              <p>
                Send us an email with your query and our team will get back to
                you within 24 hours.
              </p>
              <p><strong>Email:</strong> <EMAIL></p>
              <p><strong>Response:</strong> Within 24 hours</p>
              <a href="mailto:<EMAIL>" class="btn-primary"
                >Email Us</a
              >
            </div>
          </div>

          <!-- Support Ticket Form -->
          <div class="support-ticket">
            <div class="form-success" id="ticketSuccess">
              <p>
                <strong>Thank you for submitting your support ticket!</strong>
                Our team will review your request and respond within 24 hours.
                Your ticket number is
                <strong>#GW<span id="ticketNumber">12345</span></strong
                >.
              </p>
            </div>

            <h2>Submit a Support Ticket</h2>
            <form id="supportForm">
              <div class="form-row">
                <div class="form-group">
                  <label for="name">Full Name</label>
                  <input
                    type="text"
                    id="name"
                    class="form-control"
                    placeholder="Enter your full name"
                    required
                  />
                </div>

                <div class="form-group">
                  <label for="email">Email Address</label>
                  <input
                    type="email"
                    id="email"
                    class="form-control"
                    placeholder="Enter your email address"
                    required
                  />
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label for="phone">Phone Number</label>
                  <input
                    type="tel"
                    id="phone"
                    class="form-control"
                    placeholder="Enter your phone number"
                  />
                </div>

                <div class="form-group">
                  <label for="policyNumber"
                    >Policy Number (if applicable)</label
                  >
                  <input
                    type="text"
                    id="policyNumber"
                    class="form-control"
                    placeholder="Enter your policy number"
                  />
                </div>
              </div>

              <div class="form-group">
                <label for="issueType">Issue Type</label>
                <select id="issueType" class="form-control" required>
                  <option value="" selected disabled>Select issue type</option>
                  <option value="Account Access">Account Access</option>
                  <option value="Policy Information">Policy Information</option>
                  <option value="Payment Issue">Payment Issue</option>
                  <option value="Claim Status">Claim Status</option>
                  <option value="Technical Issue">Technical Issue</option>
                  <option value="Other">Other</option>
                </select>
              </div>

              <div class="form-group">
                <label for="priority">Priority</label>
                <select id="priority" class="form-control" required>
                  <option value="" selected disabled>Select priority</option>
                  <option value="Low">Low - General inquiry</option>
                  <option value="Medium">
                    Medium - Issue affecting service
                  </option>
                  <option value="High">High - Urgent matter</option>
                </select>
              </div>

              <div class="form-group">
                <label for="description">Describe Your Issue</label>
                <textarea
                  id="description"
                  class="form-control"
                  placeholder="Please provide details about your issue"
                  required
                ></textarea>
              </div>

              <div class="form-group">
                <label for="attachment">Attach Files (optional)</label>
                <input type="file" id="attachment" class="form-control" />
                <small
                  >Max file size: 5MB. Accepted formats: PDF, JPG, PNG</small
                >
              </div>

              <button type="submit" class="btn-primary">Submit Ticket</button>
            </form>
          </div>

          <!-- FAQ Preview -->
          <div class="faq-preview">
            <h2>Frequently Asked Questions</h2>

            <div class="faq-items">
              <div class="faq-item">
                <div class="faq-question">How do I check my policy status?</div>
                <div class="faq-answer">
                  <p>
                    You can check your policy status by logging into your
                    account on our website or mobile app. Navigate to the "My
                    Policies" section to view all your active policies and their
                    current status. Alternatively, you can call our customer
                    support at 1800 123 456 or email <NAME_EMAIL>
                    with your policy number.
                  </p>
                </div>
              </div>

              <div class="faq-item">
                <div class="faq-question">
                  How do I update my contact information?
                </div>
                <div class="faq-answer">
                  <p>
                    To update your contact information, log into your account
                    and go to the "Profile" or "Account Settings" section. Here,
                    you can update your phone number, email address, and mailing
                    address. For security reasons, some changes may require
                    additional verification. If you're having trouble, please
                    contact our customer support team for assistance.
                  </p>
                </div>
              </div>

              <div class="faq-item">
                <div class="faq-question">
                  What should I do if I forgot my password?
                </div>
                <div class="faq-answer">
                  <p>
                    If you've forgotten your password, click on the "Forgot
                    Password" link on the login page. Enter your registered
                    email address, and we'll send you a password reset link.
                    Follow the instructions in the email to create a new
                    password. If you don't receive the email within a few
                    minutes, check your spam folder or contact our support team
                    for assistance.
                  </p>
                </div>
              </div>
            </div>

            <div class="faq-more">
              <a href="faqs.html" class="btn-outline">View All FAQs</a>
            </div>
          </div>

          <!-- Support Resources -->
          <div class="support-resources">
            <div class="section-header">
              <span class="section-subtitle">Self-Help Resources</span>
              <h2>
                Helpful Resources & <span class="guide-txt"> Guides</span>
              </h2>
              <p>
                Explore our knowledge base to find answers and learn more about
                our services
              </p>
            </div>

            <div class="resources-grid">
              <div class="resource-card">
                <div class="resource-image">
                  <i class="fas fa-book"></i>
                </div>
                <div class="resource-content">
                  <h3>User Guides</h3>
                  <p>
                    Step-by-step guides to help you navigate our platform and
                    make the most of your financial products.
                  </p>
                  <a href="#" class="btn-outline">View Guides</a>
                </div>
              </div>

              <div class="resource-card">
                <div class="resource-image">
                  <i class="fas fa-video"></i>
                </div>
                <div class="resource-content">
                  <h3>Video Tutorials</h3>
                  <p>
                    Watch our video tutorials to learn how to use our services,
                    manage your investments, and more.
                  </p>
                  <a href="#" class="btn-outline">Watch Videos</a>
                </div>
              </div>

              <div class="resource-card">
                <div class="resource-image">
                  <i class="fas fa-file-alt"></i>
                </div>
                <div class="resource-content">
                  <h3>Document Center</h3>
                  <p>
                    Access and download important documents, forms, and
                    brochures related to your financial products.
                  </p>
                  <a href="#" class="btn-outline">Access Documents</a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Animated Shapes -->
      <div class="shape-animation support-shape-1"></div>
      <div class="shape-animation support-shape-2"></div>
    </section>

    <!-- Footer Section -->
    <footer>
      <div class="container">
        <div class="footer-content">
          <div class="footer-logo">
            <div class="footer-logo-img">
              <img
                src="./images/growwell_logo_01.webp"
                alt="Growwell Logo"
                loading="lazy"
                class="footer-logo-image"
              />
            </div>
            <p>Your partner for financial growth and security</p>
            <div class="social-icons">
              <a
                href="https://www.facebook.com/growwellimf/"
                target="/"
                aria-label="Follow us on Facebook"
                ><i class="fab fa-facebook-f"></i
              ></a>
              <a
                href="https://x.com/Growwell__?t=QZcsQv6QemM2qT4bfejZeg&s=08"
                target="/"
                aria-label="Follow us on X"
                ><i class="fab fa-twitter"></i
              ></a>
              <a
                href="https://www.linkedin.com/company/grow-well-imf/about/"
                target="/"
                aria-label="Follow us on Linkedin"
                ><i class="fab fa-linkedin-in"></i
              ></a>
              <a
                href="https://www.instagram.com/growwell.imf/"
                target="/"
                aria-label="Follow us on Instagram"
                ><i class="fab fa-instagram"></i
              ></a>
            </div>
          </div>
          <div class="footer-links">
            <div class="footer-column">
              <h3>Quick Links</h3>
              <ul>
                <li><a href="index.html">Home</a></li>
                <li><a href="./investment-plans.html">Products</a></li>
                <li><a href="./renew-policy.html">Renew</a></li>
                <li><a href="./advisor.html">Testimonials</a></li>
                <li><a href="./contact.html">Contact</a></li>
              </ul>
            </div>
            <div class="footer-column">
              <h3>Services</h3>
              <ul>
                <li><a href="investment-plans.html">Financial Planning</a></li>
                <li><a href="child-plans.html">Child Planning</a></li>
                <li><a href="investment-plans.html">Investment Planning</a></li>
                <li><a href="retirement-plans.html">Retirement Planning</a></li>
                <li><a href="term-insurance.html">Term Insurance</a></li>
                <li><a href="#">Tax Advisory</a></li>
              </ul>
            </div>
            <div class="footer-column">
              <h3>Resources</h3>
              <ul>
                <li><a href="leadership.html">Leadership</a></li>
                <li><a href="advisor.html">Advisor</a></li>
                <li>
                  <a href="retirement-plans.html">Financial Calculators</a>
                </li>
                <li><a href="faqs.html">FAQs</a></li>
                <li><a href="contact.html">contact</a></li>
              </ul>
            </div>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2023 Growwell Financial Advisors. All rights reserved.</p>
          <ul class="footer-legal">
            <li><a href="#">Privacy Policy</a></li>
            <li><a href="#">Terms of Service</a></li>
            <li><a href="#">Cookie Policy</a></li>
          </ul>
        </div>
      </div>
    </footer>
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "Growwell",
        "url": "https://growwellimf.com",
        "logo": "https://growwellimf.com/images/growwell_logo_01.webp",
        "sameAs": [
          "https://www.facebook.com/growwellimf/",
          "https://www.instagram.com/growwell.imf/",
          "https://x.com/Growwell__?t=QZcsQv6QemM2qT4bfejZeg&s=08",
          "https://www.linkedin.com/company/grow-well-imf/about/"
        ]
      }
    </script>
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Mobile Menu Toggle
        const hamburger = document.getElementById("hamburger");
        const navMenu = document.getElementById("nav-menu");
        const body = document.body;

        if (hamburger) {
          hamburger.addEventListener("click", function () {
            navMenu.classList.toggle("active");
            body.classList.toggle("nav-active");

            // Toggle hamburger appearance
            const bars = this.querySelectorAll(".bar");
            bars.forEach((bar) => bar.classList.toggle("active"));

            console.log(
              "Hamburger clicked. Nav active:",
              navMenu.classList.contains("active")
            );
          });
        }

        // Dropdown Toggle for Mobile
        const dropdownToggles = document.querySelectorAll(".dropdown-toggle");
        dropdownToggles.forEach((toggle) => {
          toggle.addEventListener("click", function (e) {
            // Only run on mobile (width <= 992px)
            if (window.innerWidth <= 992) {
              e.preventDefault();
              e.stopPropagation();

              // Find the dropdown menu that is a sibling of this toggle
              const dropdownMenu = this.nextElementSibling;

              // Close all other dropdown menus
              document.querySelectorAll(".dropdown-menu").forEach((menu) => {
                if (menu !== dropdownMenu) {
                  menu.classList.remove("active");
                }
              });

              // Remove active class from all other toggles
              document.querySelectorAll(".dropdown-toggle").forEach((tog) => {
                if (tog !== this) {
                  tog.classList.remove("active");
                }
              });

              // Toggle active class on this dropdown menu and toggle
              dropdownMenu.classList.toggle("active");
              this.classList.toggle("active");
            }
          });
        });

        // Close menu when clicking outside
        document.addEventListener("click", function (e) {
          if (window.innerWidth <= 992) {
            if (!e.target.closest("nav") && !e.target.closest("#hamburger")) {
              // Close the navigation menu
              if (navMenu.classList.contains("active")) {
                navMenu.classList.remove("active");
                body.classList.remove("nav-active");

                // Reset hamburger icon
                const bars = hamburger.querySelectorAll(".bar");
                bars.forEach((bar) => bar.classList.remove("active"));

                // Close all dropdown menus
                document.querySelectorAll(".dropdown-menu").forEach((menu) => {
                  menu.classList.remove("active");
                });

                // Remove active class from all toggles
                document.querySelectorAll(".dropdown-toggle").forEach((tog) => {
                  tog.classList.remove("active");
                });
              }
            }
          }
        });

        // FAQ toggle functionality
        const faqItems = document.querySelectorAll(".faq-item");
        faqItems.forEach((item) => {
          const question = item.querySelector(".faq-question");
          if (question) {
            question.addEventListener("click", function () {
              item.classList.toggle("active");
            });
          }
        });

        // Support ticket form submission
        const supportForm = document.getElementById("supportForm");
        const ticketSuccess = document.getElementById("ticketSuccess");
        const ticketNumber = document.getElementById("ticketNumber");

        if (supportForm) {
          supportForm.addEventListener("submit", function (e) {
            e.preventDefault();

            // Generate random ticket number for demo
            const randomTicket = Math.floor(100000 + Math.random() * 900000);
            ticketNumber.textContent = randomTicket;

            // Show success message
            ticketSuccess.style.display = "block";

            // Scroll to success message
            ticketSuccess.scrollIntoView({ behavior: "smooth" });

            // Reset form
            supportForm.reset();
          });
        }
      });
    </script>
  </body>
</html>
