/* Investment Plans Page Specific Styles */

/* Hero Section */
.investment-hero {
    background: linear-gradient(to right, #f8faff 0%, #edf4ff 100%);
    padding: 120px 0 100px;
    position: relative;
    overflow: hidden;
    color: var(--dark-color);
    
}

.investment-hero::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: url('../images/investment-pattern.svg') no-repeat center;
    background-size: cover;
    opacity: 0.05;
    z-index: 1;
}
.investment-txt{
    color: var(--accent-color);
}
.calculate-txt {
    color: var(--accent-color);
}

/* Decorative shapes */
.investment-hero::after {
    content: '';
    position: absolute;
    top: -150px;
    right: -150px;
    width: 400px;
    height: 400px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(30, 86, 160, 0.08) 0%, rgba(30, 86, 160, 0.03) 50%, rgba(30, 86, 160, 0) 70%);
    z-index: 1;
    animation: pulse 10s infinite alternate;
}

.investment-hero-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    position: relative;
    z-index: 2;
    max-width: 900px;
    margin: 0 auto;
}

.hero-badge {
    display: inline-block;
    background: var(--dark-color);
    color: white;
    border: none;
    padding: 10px 24px;
    border-radius: 30px;
    font-size: 0.95rem;
    font-weight: 600;
    margin-bottom: 35px;
    box-shadow: 0 8px 15px rgba(30, 86, 160, 0.15);
    transition: all 0.3s ease;
    transform: translateY(0);
    margin-top: 1.2rem;
}

.hero-badge:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 20px rgba(30, 86, 160, 0.2);
}

.investment-hero h1 {
    font-size: 3.2rem;
    font-weight: 800;
    margin-bottom: 30px;
    line-height: 1.2;
    color: var(--dark-color);
    letter-spacing: -0.5px;
    position: relative;
}

.gradient-text {
    color: var(--accent-color);
    position: relative;
    display: inline-block;
    transition: all 0.3s ease;
}

.gradient-text::after {
    content: '';
    position: absolute;
    bottom: 5px;
    left: 0;
    width: 100%;
    height: 8px;
    background-color: rgba(30, 86, 160, 0.15);
    border-radius: 10px;
    z-index: -1;
}

.investment-hero p {
    font-size: 1.15rem;
    margin-bottom: 45px;
    color: var(--text-light);
    line-height: 1.7;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.investment-hero-cta {
    display: flex;
    gap: 20px;
    margin-top: 15px;
    justify-content: center;
}

.btn-hero {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 15px 32px;
    border-radius: 6px;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.btn-primary.btn-hero {
    background: var(--accent-color);
    color: white;
    box-shadow: 0 8px 15px rgba(30, 86, 160, 0.2);
    position: relative;
    overflow: hidden;
}

.btn-primary.btn-hero::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: 0.5s;
}

.btn-primary.btn-hero:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 20px rgba(30, 86, 160, 0.3);
}

.btn-primary.btn-hero:hover::after {
    left: 100%;
}

.btn-secondary.btn-hero {
    background: white;
    color: var(--primary-color);
    border: 1px solid #e1e8f5;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.03);
    position: relative;
    z-index: 1;
}

.btn-secondary.btn-hero:hover {
    transform: translateY(-3px);
    border-color: var(--primary-color);
    background: white;
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.05);
}

.investment-hero-visual {
    position: relative;
    height: 500px;
}

.hero-image-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
    display: none; /* Hide all floating elements */
}

.floating-card {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 12px 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    animation: none; /* Disable animation */
    display: none; /* Hide floating cards */
}

.floating-card i {
    font-size: 1.2rem;
    color: var(--primary-color);
}

.floating-card span {
    font-weight: 600;
    font-size: 0.85rem;
}

.card-1 {
    top: 20%;
    left: 15%;
    animation-delay: 0.5s;
}

.card-2 {
    top: 50%;
    right: 15%;
    animation-delay: 1.5s;
}

.card-3 {
    bottom: 15%;
    left: 20%;
    animation-delay: 1s;
}

.floating-shape {
    position: absolute;
    border-radius: 50%;
    opacity: 0.2;
    animation: none; /* Disable animation */
    display: none; /* Hide floating shapes */
}

.shape-1 {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    top: 15%;
    right: 20%;
    animation-delay: 0s;
}

.shape-2 {
    width: 40px;
    height: 40px;
    background: var(--primary-light);
    bottom: 30%;
    right: 30%;
    animation-delay: 0s;
}

.shape-3 {
    width: 80px;
    height: 80px;
    background: var(--dark-color);
    bottom: 20%;
    right: 15%;
    animation-delay: 0s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-15px);
    }
}

.hero-chart {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: flex-end;
    gap: 15px;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 25px 30px;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
    width: 300px;
    height: 300px;
}

.chart-bar {
    flex: 1;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px 8px 0 0;
    position: relative;
    transition: all 0.5s ease;
}

.chart-bar::before {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 10px;
    height: 10px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
}

.chart-bar.active {
    background: linear-gradient(180deg, var(--primary-color), var(--primary-light));
    box-shadow: 0 0 20px rgba(30, 86, 160, 0.4);
}

.chart-bar.projected {
    background: linear-gradient(180deg, rgba(30, 86, 160, 0.5), rgba(59, 125, 212, 0.5));
    border: 1px dashed var(--primary-color);
}

.bar-label {
    position: absolute;
    bottom: -35px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.8rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.7);
}

.hero-partners {
    margin-top: 70px;
    text-align: center;
    padding-top: 30px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.hero-partners p {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 20px;
}

.partner-logos {
    display: flex;
    justify-content: center;
    gap: 40px;
    flex-wrap: wrap;
}

.partner-logo {
    font-size: 1rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    gap: 10px;
}

.partner-logo i {
    font-size: 1.2rem;
    color: var(--primary-color);
}

/* Media Queries for Hero Section */
@media screen and (max-width: 1100px) {
    .investment-hero-wrapper {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .investment-hero-content {
        max-width: 100%;
        text-align: center;
    }
    
    .investment-hero-stats {
        justify-content: center;
    }
    
    .investment-hero-cta {
        justify-content: center;
    }
    
    .investment-hero-visual {
        height: 400px;
        margin: 0 auto;
        max-width: 500px;
    }
    
    .hero-chart {
        width: 250px;
        height: 250px;
    }
}

@media screen and (max-width: 768px) {
    .investment-hero {
        padding: 100px 0 80px;
    }
    
    .investment-hero h1 {
        font-size: 2.6rem;
    }
    
    .investment-hero p {
        font-size: 1.05rem;
        padding: 0 20px;
    }
    
    .investment-hero-cta {
        flex-direction: column;
        width: 100%;
        max-width: 300px;
        margin-left: auto;
        margin-right: auto;
    }
    
    .btn-hero {
        width: 100%;
    }
    
    .investment-hero-stats {
        flex-direction: column;
        gap: 15px;
        align-items: center;
    }
    
    .hero-stat {
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        padding-bottom: 15px;
        width: 100%;
        text-align: center;
    }
    
    .hero-stat::after {
        display: none;
    }
    
    .hero-stat:last-child {
        border-bottom: none;
    }
    
    .investment-hero-visual {
        height: 350px;
    }
    
    .floating-card {
        padding: 10px 15px;
        font-size: 0.8rem;
    }
    
    .hero-chart {
        width: 220px;
        height: 220px;
        padding: 20px;
    }
    
    .partner-logos {
        gap: 20px;
    }
    
    .partner-logo {
        font-size: 0.9rem;
    }
}

/* Benefits Section */
.investment-benefits {
    padding: 100px 0;
    background-color: #f8f9fb;
    position: relative;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 60px;
}

.benefit-card {
    background: #ffffff;
    border-radius: 15px;
    padding: 40px 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    border: 2px solid transparent;
}

.benefit-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border-color: #ffbb33;
}

/* Removing different border colors for individual cards */
.benefit-card:nth-child(1):hover,
.benefit-card:nth-child(2):hover,
.benefit-card:nth-child(3):hover,
.benefit-card:nth-child(4):hover,
.benefit-card:nth-child(5):hover,
.benefit-card:nth-child(6):hover {
    border-color: #ffbb33; /* Yellow for all cards */
}

.benefit-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #e7f0ff 0%, #f1f7ff 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 25px;
}

.benefit-icon i {
    font-size: 36px;
    /* color: #4864e0; */
    color: var(--accent-color)
}

.benefit-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: #0c2340;
}

.benefit-description {
    color: #647082;
    line-height: 1.6;
    font-size: 1rem;
}

/* Investment Plans Section */
.investment-plans {
    padding: 100px 0;
    background: linear-gradient(180deg, #ffffff 0%, #f3f6fd 100%);
}

.plans-tabs {
    display: flex;
    justify-content: center;
    margin: 40px 0;
    gap: 15px;
}

.plan-tab {
    padding: 12px 25px;
    border-radius: 30px;
    background-color: #f0f3f9;
    color: #647082;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.plan-tab.active, .plan-tab:hover {
    /* background-color: #4864e0; */
    background: var(--accent-color);
    color: #fff;
    box-shadow: 0 5px 15px rgba(72, 100, 224, 0.25);
}

.plans-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 30px;
}

.plan-card {
    background: #ffffff;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.04);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    border: 3px solid transparent;
    z-index: 1;
}

.plan-card::after {
    display: none;
}

.plan-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.1);
    /* border-color: #4864e0; */
    border-color: var(--accent-color);
}
.plan-card.popular {
    border-color: var(--accent-color);
}

.plan-card.popular:hover {
    border-color: var(--accent-color);
    box-shadow: 0 20px 50px rgba(72, 100, 224, 0.2);
}

.plan-badge {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(72, 100, 224, 0.1);
    color: #4864e0;
    font-size: 0.85rem;
    font-weight: 600;
    padding: 4px 12px;
    border-radius: 20px;
}

.plan-card.popular .plan-badge {
    background: #4864e0;
    color: #fff;
}

.plan-header {
    margin-bottom: 25px;
}

.plan-name {
    font-size: 1.6rem;
    font-weight: 700;
    color: #0c2340;
    margin-bottom: 10px;
}

.plan-description {
    color: #647082;
    margin-bottom: 20px;
    line-height: 1.5;
}

.plan-price {
    font-size: 2.5rem;
    font-weight: 700;
    /* color: #4864e0; */
    color: var(--accent-color);
    margin-bottom: 5px;
}

.plan-price sup {
    font-size: 1rem;
    top: -1em;
}

.plan-duration {
    color: #647082;
    font-size: 0.9rem;
}

.plan-features {
    margin: 30px 0;
    padding: 0;
    list-style: none;
}

.plan-features li {
    padding: 12px 0;
    color: #647082;
    display: flex;
    align-items: center;
    gap: 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.plan-features li:last-child {
    border-bottom: none;
}

.plan-features i {
    color: #4864e0;
    font-size: 1.2rem;
}

.plan-cta {
    text-align: center;
    margin-top: 20px;
}

.btn-plan {
    display: inline-block;
    padding: 12px 30px;
    border-radius: 30px;
    /* background: linear-gradient(135deg, #4864e0 0%, #7788ff 100%); */
    background: var(--accent-color);
    color: #fff;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(72, 100, 224, 0.2);
}

.btn-plan:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(72, 100, 224, 0.3);
    color: #fff;
}

.plan-card.popular .btn-plan {
    /* background: linear-gradient(135deg, #0c2340 0%, #1a3a6c 100%); */
    background: var(--accent-color)
}

/* Investment Calculator Section - Responsive Update */
.investment-calculator {
    padding: 100px 0;
    background: #fff;
    position: relative;
}

.section-header.centered {
    text-align: center;
    margin-bottom: 30px;
}

.calculator-container {
    display: flex;
    background: white;
    border-radius: 20px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    margin-top: 50px;
    flex-wrap: wrap; /* Allow wrapping on smaller screens */
}

.calculator-form {
    flex: 1;
    padding: 40px;
    background: #f8f9fb;
    border-right: 1px solid #f0f3f8;
    min-width: 300px; /* Ensure minimum width */
}

.calculator-group {
    margin-bottom: 25px;
}

.calculator-label {
    display: block;
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
    font-size: 1rem;
}

.calculator-input {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #e1e8f5;
    border-radius: 6px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.calculator-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(30, 86, 160, 0.1);
    outline: none;
}

.calculator-slider {
    -webkit-appearance: none;
    width: 100%;
    height: 8px;
    border-radius: 5px;
    background: #e1e8f5;
    outline: none;
    margin: 15px 0;
}

.calculator-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
}

.calculator-result {
    flex: 1;
    padding: 40px;
    background: linear-gradient(135deg, #0c2340 0%, #1a3a6c 100%);
    color: #fff;
    min-width: 300px; /* Ensure minimum width */
}

.result-title {
    font-size: 1.6rem;
    font-weight: 700;
    margin-bottom: 25px;
    text-align: center;
    color: #fff;
}

.result-chart {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.result-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.result-stat {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 15px;
    text-align: center;
}

.stat-value {
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 5px;
    color: #fff;
}

.stat-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
}

/* Media queries for calculator responsiveness */
@media screen and (max-width: 1200px) {
    .calculator-container {
        margin: 40px auto;
        max-width: 95%;
    }
}

@media screen and (max-width: 992px) {
    .calculator-container {
        flex-direction: column;
        max-width: 100%;
        margin: 30px auto;
    }
    
    .calculator-form, .calculator-result {
        width: 100%;
        border-right: none;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .result-stats {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
}

@media screen and (max-width: 768px) {
    .investment-calculator {
        padding: 60px 0;
    }
    
    .calculator-form, .calculator-result {
        padding: 25px;
    }
    
    .calculator-label {
        font-size: 0.9rem;
    }
    
    .calculator-input {
        padding: 10px;
        font-size: 0.9rem;
    }
    
    .result-title {
        font-size: 1.2rem;
        margin-bottom: 15px;
    }
    
    .result-stats {
        gap: 15px;
    }
    
    .stat-value {
        font-size: 1.2rem;
    }
    
    .stat-label {
        font-size: 0.8rem;
    }
}

@media screen and (max-width: 576px) {
    .investment-calculator {
        padding: 40px 0;
    }
    
    .calculator-form, .calculator-result {
        padding: 20px;
    }
    
    .calculator-group {
        margin-bottom: 15px;
    }
    
    .result-stats {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .result-stat {
        padding: 12px;
    }
    
    .calculator-slider {
        margin: 10px 0;
    }
    
    #period-value, #rate-value {
        font-size: 0.85rem;
    }
}

/* Process Section */
.investment-process {
    padding: 120px 0 100px;
    background: linear-gradient(135deg, #f8faff 0%, #f0f5ff 100%);
    position: relative;
    overflow: hidden;
}

.investment-process::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('../images/investment-pattern.svg') no-repeat center;
    background-size: cover;
    opacity: 0.05;
    z-index: 1;
}

.investment-process .container {
    position: relative;
    z-index: 2;
}

.process-steps {
    max-width: 1200px;
    margin: 80px auto 0;
    position: relative;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
}

/* Remove the vertical line since we're using a grid layout now */
.process-steps::before {
    display: none;
}

.process-step {
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 2;
    background: #ffffff;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.06);
    transition: all 0.4s ease;
    overflow: hidden;
    height: 100%;
    border: 2px solid transparent;
}

.process-step:hover {
    transform: translateY(-15px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
    border-color: #ffbb33;
}

.process-step-number {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #4864e0 0%, #7788ff 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 2rem;
    font-weight: 700;
    margin: -40px auto 20px;
    box-shadow: 0 15px 30px rgba(72, 100, 224, 0.3);
    position: relative;
    z-index: 3;
    transition: all 0.3s ease;
}

.process-step:hover .process-step-number {
    background: linear-gradient(135deg, #ffbb33 0%, #ffce66 100%);
    transform: scale(1.1) rotate(360deg);
    transition: all 0.5s ease;
}

.process-step-content {
    padding: 30px 35px 50px;
    text-align: center;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.process-step-title {
    font-size: 1.6rem;
    font-weight: 700;
    color: #0c2340;
    margin-bottom: 25px;
    position: relative;
    padding-bottom: 18px;
}

.process-step-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 4px;
    background: linear-gradient(90deg, #4864e0, #7788ff);
    border-radius: 4px;
    transition: all 0.3s ease;
}

.process-step:hover .process-step-title::after {
    width: 100px;
    background: linear-gradient(90deg, #ffbb33, #ffce66);
}

.process-step-description {
    color: #647082;
    line-height: 1.8;
    font-size: 1.1rem;
}

.process-step-icon {
    margin-bottom: 25px;
    width: 100px;
    height: 100px;
    background: rgba(72, 100, 224, 0.1);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.4s ease;
}

.process-step-icon i {
    font-size: 2.8rem;
    transition: all 0.4s ease;
}

.process-step:hover .process-step-icon {
    background: rgba(255, 187, 51, 0.1);
    transform: rotate(10deg);
}

.process-step:hover .process-step-icon i {
    transform: scale(1.2);
}

/* Process step specific icons */
.process-step:nth-child(1) .process-step-icon i {
    color: #4864e0; /* Blue */
}

.process-step:nth-child(2) .process-step-icon i {
    color: #2cb67d; /* Green */
}

.process-step:nth-child(3) .process-step-icon i {
    color: #ff7849; /* Orange */
}

.process-step:nth-child(4) .process-step-icon i {
    color: #7e57c2; /* Purple */
}

.process-step:nth-child(5) .process-step-icon i {
    color: #ff5e7d; /* Pink */
}

.process-step:hover .process-step-icon i {
    color: #ffbb33;
}

.process-cta {
    margin-top: 80px;
    text-align: center;
}

.btn-process {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 18px 38px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1.15rem;
    transition: all 0.4s ease;
    background: linear-gradient(135deg, #4864e0 0%, #7788ff 100%);
    color: white;
    text-decoration: none;
    box-shadow: 0 10px 25px rgba(72, 100, 224, 0.25);
}

.btn-process i {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.btn-process:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(255, 187, 51, 0.3);
    background: linear-gradient(135deg, #ffbb33 0%, #ffce66 100%);
}

.btn-process:hover i {
    transform: translateX(5px);
}

@media screen and (max-width: 1200px) {
    .process-steps {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media screen and (max-width: 992px) {
    .process-steps {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media screen and (max-width: 768px) {    
    .process-steps {
        grid-template-columns: 1fr;
        margin-top: 60px;
    }
    
    .process-step {
        max-width: 450px;
        margin-left: auto;
        margin-right: auto;
    }
    
    .process-step-title {
        font-size: 1.5rem;
    }
    
    .process-step-description {
        font-size: 1rem;
    }
    
    .process-step-icon {
        width: 80px;
        height: 80px;
    }
    
    .process-step-icon i {
        font-size: 2.4rem;
    }
}

/* FAQ Section */
.investment-faq {
    padding: 100px 0;
    background-color: #f8f9fb;
}

.faq-container {
    max-width: 800px;
    margin: 50px auto 0;
}

.faq-item {
    background: #fff;
    border-radius: 15px;
    margin-bottom: 20px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.03);
}

.faq-question {
    padding: 25px 30px;
    font-size: 1.2rem;
    font-weight: 600;
    color: #0c2340;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.faq-question i {
    color: #4864e0;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.faq-item.active .faq-question {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.faq-item.active .faq-question i {
    transform: rotate(180deg);
}

.faq-answer {
    padding: 0 30px;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item.active .faq-answer {
    padding: 25px 30px;
    max-height: 500px;
}

.faq-answer-content {
    color: #647082;
    line-height: 1.7;
}

/* Media Queries */
@media screen and (max-width: 992px) {
    .benefits-grid, .plans-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .calculator-container {
        grid-template-columns: 1fr;
        gap: 40px;
    }
}

@media screen and (max-width: 768px) {
    .investment-hero-content h1 {
        font-size: 2.5rem;
    }
    
    .investment-hero-content p {
        font-size: 1.1rem;
    }
    
    .benefits-grid, .plans-grid {
        grid-template-columns: 1fr;
    }
    
    .plans-tabs {
        flex-wrap: wrap;
    }
    
    .process-step {
        flex-direction: column;
    }
    
    .process-step-content {
        margin-left: 0;
        margin-top: 20px;
    }
    
    .process-steps::before {
        display: none;
    }
}

@media screen and (max-width: 576px) {
    .investment-hero-cta {
        flex-direction: column;
    }
}

/* Consultation Modal Styles */
.consultation-modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(12, 35, 64, 0.8);
    backdrop-filter: blur(5px);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.consultation-modal.show {
    opacity: 1;
}

.consultation-modal-content {
    position: relative;
    background-color: #fff;
    margin: 2% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    transform: translateY(-50px);
    transition: transform 0.3s ease;
    animation: slideInModal 0.3s ease forwards;
}

.consultation-modal.show .consultation-modal-content {
    transform: translateY(0);
}

@keyframes slideInModal {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.consultation-modal-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 25px 30px;
    border-radius: 15px 15px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.consultation-modal-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--accent-color);
}

.consultation-modal-header h2 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 12px;
}

.consultation-modal-header h2 i {
    color: var(--accent-color);
    font-size: 1.6rem;
}

.consultation-close {
    color: white;
    font-size: 32px;
    font-weight: 300;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
}

.consultation-close:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: rotate(90deg);
}

.consultation-modal-body {
    padding: 30px;
}

.consultation-intro {
    font-size: 1.1rem;
    color: var(--gray-color);
    margin-bottom: 30px;
    text-align: center;
    line-height: 1.6;
    padding: 20px;
    background: linear-gradient(135deg, rgba(30, 86, 160, 0.05) 0%, rgba(246, 185, 59, 0.05) 100%);
    border-radius: 10px;
    border-left: 4px solid var(--accent-color);
}

.consultation-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.95rem;
}

.form-group label i {
    color: var(--accent-color);
    width: 16px;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 12px 15px;
    border: 2px solid #e1e8f5;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #fff;
    color: var(--dark-color);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(246, 185, 59, 0.1);
    transform: translateY(-2px);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
    font-family: inherit;
}

.consultation-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e1e8f5;
}

.consultation-actions .btn {
    padding: 12px 25px;
    border-radius: 8px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.consultation-actions .btn-primary {
    background: var(--accent-color);
    color: white;
    box-shadow: 0 4px 15px rgba(246, 185, 59, 0.3);
}

.consultation-actions .btn-primary:hover {
    background: var(--accent-dark);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(246, 185, 59, 0.4);
}

.consultation-actions .btn-secondary {
    background: #f8f9fa;
    color: var(--gray-color);
    border: 2px solid #e1e8f5;
}

.consultation-actions .btn-secondary:hover {
    background: #e9ecef;
    color: var(--dark-color);
    transform: translateY(-2px);
}

/* Mobile Responsive */
@media screen and (max-width: 768px) {
    .consultation-modal-content {
        width: 95%;
        margin: 5% auto;
        max-height: 85vh;
    }

    .consultation-modal-header {
        padding: 20px;
    }

    .consultation-modal-header h2 {
        font-size: 1.5rem;
    }

    .consultation-modal-body {
        padding: 20px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .consultation-actions {
        flex-direction: column-reverse;
    }

    .consultation-actions .btn {
        width: 100%;
        justify-content: center;
    }
}

@media screen and (max-width: 480px) {
    .consultation-modal-content {
        width: 98%;
        margin: 2% auto;
        border-radius: 10px;
    }

    .consultation-modal-header {
        padding: 15px;
        border-radius: 10px 10px 0 0;
    }

    .consultation-modal-header h2 {
        font-size: 1.3rem;
    }

    .consultation-modal-body {
        padding: 15px;
    }

    .consultation-intro {
        padding: 15px;
        font-size: 1rem;
    }
}