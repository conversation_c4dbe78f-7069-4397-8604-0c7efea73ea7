 /* Support Page Specific Styles */
 /* Support Hero Section */
 :root {
    --primary-color: #1e56a0;
    --primary-dark: #164584;
    --primary-light: #3b7dd4;
    --secondary-color: #163172;
    --accent-color: #f6b93b;
    --accent-dark: #e6a012;
    --dark-color: #0c2340;
    --light-color: #f4f7fc;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --gray-color: #6c757d;
    --gray-light-color: #f8f9fa;
    --shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
    --shadow-intense: 0 15px 40px rgba(0, 0, 0, 0.15);
    --shadow-sm: 0 3px 12px rgba(0, 0, 0, 0.05);
    --transition: all 0.3s ease;
    --border-radius: 12px;
    --border-radius-sm: 8px;
    --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-dark) 100%);
    --gradient-bg: linear-gradient(135deg, rgba(244, 247, 252, 0.95) 0%, rgba(214, 229, 250, 0.95) 100%);
    
    /* Text colors */
    --text-color: #333;
    --text-light: #6c757d;
    --bg-color: #fff;
    --card-bg: #fff;
}

 .support-hero {
    background: linear-gradient(135deg, var(--accent-color), var(--success-color));
        color: #ffffff;
        padding: 100px 20px;
        text-align: center;
        /* margin-top: 100px; */
        margin-top: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;
}

.support-hero::before {
  content: "";
  position: absolute;
  width: 400px;
  height: 400px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  top: -100px;
  left: -100px;
  z-index: 0;
}


.support-hero::after {
  content: "";
  position: absolute;
  width: 300px;
  height: 300px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  bottom: -80px;
  right: -80px;
  z-index: 0;
}

.support-txt {
    color: var(--accent-color);
}

.support-hero .container {
  position: relative;
  z-index: 1;
  max-width: 900px;
  margin: 0 auto;
}

.support-hero-content h1 {
  font-size: 3rem;
  margin-bottom: 20px;
  font-weight: 700;
  line-height: 1.2;
}

.support-hero-content p {
  font-size: 1.2rem;
  color: #cfd8dc;
}

        .support-section {
            background-color: var(--light-color);
            position: relative;
            overflow: hidden;
        }
        
        .support-container {
            position: relative;
            z-index: 2;
        }
        
        .support-options {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 60px;
        }
        
        .support-option {
            background: #fff;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            padding: 30px;
            transition: var(--transition);
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .support-option:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow);
        }
        
        .support-option::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: var(--accent-color);
        }
        
        .support-icon {
            width: 80px;
            height: 80px;
            background: rgba(30, 86, 160, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: var(--accent-color);
            font-size: 32px;
            transition: var(--transition);
        }
        
        .support-option:hover .support-icon {
            background: var(--accent-color);
            color: #fff;
            transform: scale(1.1);
        }
        
       

        .support-option h3 {
            margin-bottom: 15px;
            color: var(--dark-color);
            font-size: 22px;
        }
        
        .support-option p {
            color: var(--gray-color);
            margin-bottom: 20px;
        }
        
        .support-option .btn-primary {
            display: inline-block;
            padding: 10px 25px;
            background: var(--accent-color);
            color: #fff;
            border-radius: 30px;
            font-weight: 500;
            transition: var(--transition);
        }
        
        .support-option .btn-primary:hover {
            background: var(--accent-dark);
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(30, 86, 160, 0.3);
        }
        
        .support-ticket {
            background: #fff;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 40px;
            margin-bottom: 60px;
            position: relative;
            overflow: hidden;
        }
        
        .support-ticket::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: var(--accent-color);
        }
        
        .support-ticket h2 {
            margin-bottom: 30px;
            color: var(--dark-color);
            position: relative;
            display: inline-block;
        }
        
        .support-ticket h2::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            width: 50px;
            height: 3px;
            background: var(--primary-color);
            border-radius: 2px;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--dark-color);
        }
        
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #e1e1e1;
            border-radius: var(--border-radius-sm);
            font-family: 'Poppins', sans-serif;
            font-size: 16px;
            transition: var(--transition);
        }
        
        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(30, 86, 160, 0.1);
        }
        
        textarea.form-control {
            min-height: 150px;
            resize: vertical;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        /* Tablet Devices */
        @media (max-width: 992px) {
            .support-hero-content h1 {
                font-size: 2.8rem;
            }
            .support-container {
                max-width: 95%;
            }
            .support-option {
                padding: 25px 20px;
            }
            .support-channels {
                gap: 20px;
            }
        }
        
        /* Mobile Devices */
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            .support-options {
                grid-template-columns: 1fr;
            }
            .support-hero-content h1 {
                font-size: 2.5rem;
            }
            .faq-item {
                padding: 20px;
            }
            .support-hero {
                padding: 80px 20px;
            }
            .support-channels {
                flex-direction: column;
            }
            .support-channel {
                width: 100%;
            }
            .contact-methods {
                flex-direction: column;
            }
            .contact-method {
                width: 100%;
                margin-bottom: 15px;
            }
        }
        
        /* Small Mobile Devices */
        @media (max-width: 576px) {
            .support-hero-content h1 {
                font-size: 2rem;
            }
            .support-hero-content p {
                font-size: 1rem;
            }
            .support-hero {
                padding: 60px 15px;
            }
            .section-header h2 {
                font-size: 1.8rem;
            }
            .support-option h3 {
                font-size: 1.3rem;
            }
            .support-option {
                padding: 20px 15px;
            }
            .faq-item h3 {
                font-size: 1.1rem;
            }
            .faq-item p {
                font-size: 0.9rem;
            }
            .support-option-icon {
                width: 50px;
                height: 50px;
                font-size: 1.5rem;
            }
        }
        
        .form-success {
            display: none;
            background-color: rgba(40, 167, 69, 0.1);
            border-left: 4px solid #28a745;
            padding: 15px;
            border-radius: var(--border-radius-sm);
            margin-bottom: 20px;
            animation: fadeIn 0.5s ease;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .faq-preview {
            background: #fff;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 40px;
            margin-bottom: 60px;
            position: relative;
        }

        .guide-txt{
            color: var(--accent-color);
        }
        
        .faq-preview::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: var(--accent-color);
        }
        
        .faq-preview h2 {
            margin-bottom: 30px;
            color: var(--dark-color);
            position: relative;
            display: inline-block;
        }
        
        .faq-preview h2::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            width: 50px;
            height: 3px;
            background: var(--accent-color);
            border-radius: 2px;
        }
        
        .faq-item {
            background: var(--light-color);
            border-radius: var(--border-radius-sm);
            margin-bottom: 15px;
            overflow: hidden;
            transition: var(--transition);
            border-left: 4px solid var(--accent-color);
        }
        
        .faq-item:hover {
            box-shadow: var(--shadow-sm);
        }
        
        .faq-question {
            padding: 15px 20px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 600;
            color: var(--dark-color);
            position: relative;
        }
        
        .faq-question::after {
            content: '\f107';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            color: var(--primary-color);
            transition: var(--transition);
        }
        
        .faq-item.active .faq-question::after {
            transform: rotate(180deg);
        }
        
        .faq-answer {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.5s ease;
            padding: 0 20px;
        }
        
        .faq-item.active .faq-answer {
            max-height: 500px;
            padding: 0 20px 15px;
        }
        
        .faq-more {
            text-align: center;
            margin-top: 30px;
        }
        
        .faq-more .btn-outline {
            display: inline-block;
            padding: 10px 25px;
            border: 1px solid var(--accent-color);
            border-radius: 30px;
            color: var(--accent-color);
            font-weight: 500;
            transition: var(--transition);
        }
        
        .faq-more .btn-outline:hover {
            background: var(--accent-color);
            color: #fff;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(30, 86, 160, 0.2);
        }
        
        .support-resources {
            margin-bottom: 60px;
        }
        
        .resources-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 30px;
        }
        
        .resource-card {
            background: #fff;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            overflow: hidden;
            transition: var(--transition);
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        
        .resource-card:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow);
        }
        
        .resource-image {
            height: 180px;
            background-color: var(--primary-light);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 50px;
        }
        
        .resource-content {
            padding: 25px;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }
        
        .resource-content h3 {
            margin-bottom: 15px;
            color: var(--dark-color);
            font-size: 20px;
        }
        
        .resource-content p {
            color: var(--gray-color);
            margin-bottom: 20px;
            flex-grow: 1;
        }
        
        .resource-content .btn-outline {
            align-self: flex-start;
            padding: 8px 20px;
            border: 1px solid var(--accent-color);
            border-radius: 30px;
            color: var(--accent-color);
            font-weight: 500;
            transition: var(--transition);
        }
        
        .resource-content .btn-outline:hover {
            background: var(--accent-color);
            color: #fff;
        }
        
        .shape-animation {
            position: absolute;
            border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
            z-index: 0;
        }
        
        .support-shape-1 {
            top: 10%;
            left: 5%;
            width: 300px;
            height: 300px;
            background-color: rgba(30, 86, 160, 0.05);
            animation: morphShape 15s linear infinite, float 10s ease-in-out infinite;
        }
        
        .support-shape-2 {
            bottom: 10%;
            right: 5%;
            width: 250px;
            height: 250px;
            background-color: rgba(246, 185, 59, 0.05);
            animation: morphShape 20s linear infinite reverse, float 15s ease-in-out infinite;
        }
        
        @keyframes morphShape {
            0% { border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%; }
            25% { border-radius: 58% 42% 75% 25% / 76% 46% 54% 24%; }
            50% { border-radius: 50% 50% 33% 67% / 55% 27% 73% 45%; }
            75% { border-radius: 33% 67% 58% 42% / 63% 68% 32% 37%; }
            100% { border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%; }
        }
        
        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
            100% { transform: translateY(0px); }
        }
    